package com.mc.tool.caesar.api.datamodel.vp;

import com.mc.tool.caesar.api.io.NormalStruct;

/**
 * .
 */
public class VpSrcConfigData extends NormalStruct {

  private Unsigned16 clipLeft = new Unsigned16();
  private Unsigned16 clipTop = new Unsigned16();
  private Unsigned16 clipWidth = new Unsigned16();
  private Unsigned16 clipHeight = new Unsigned16();
  private Unsigned16 outLeft = new Unsigned16();
  private Unsigned16 outTop = new Unsigned16();
  private Unsigned16 outWidth = new Unsigned16();
  private Unsigned16 outHeight = new Unsigned16();
  private Unsigned16 alpha = new Unsigned16();
  private Unsigned16 scaleH = new Unsigned16();
  private Unsigned16 scaleV = new Unsigned16();
  //private Unsigned8[] reserved = array(new Unsigned8[10]);

  public VpSrcConfigData() {
    setDefault();
  }

  /**
   * .
   */
  public void print() {
    System.out.println("  clipLeft:   " + clipLeft.get());
    System.out.println("  clipTop:    " + clipTop.get());
    System.out.println("  clipWidth:  " + clipWidth.get());
    System.out.println("  clipHeight: " + clipHeight.get());
    System.out.println("  outLeft:    " + outLeft.get());
    System.out.println("  outTop:     " + outTop.get());
    System.out.println("  outWidth:   " + outWidth.get());
    System.out.println("  outHeight:  " + outHeight.get());
    System.out.println("  alpha:      " + alpha.get());
    System.out.println("  scaleH:     " + scaleH.get());
    System.out.println("  scaleV:     " + scaleV.get());
  }

  void set(VideoClipData clipData) {
    clipLeft.set(clipData.getClipX());
    clipTop.set(clipData.getClipY());

    clipWidth.set(clipData.getClipWidth() == 0 ? 1 : clipData.getClipWidth());
    clipHeight.set(clipData.getClipHeight() == 0 ? 1 : clipData.getClipHeight());
    outLeft.set(clipData.getOutX());
    outTop.set(clipData.getOutY());
    outWidth.set(clipData.getOutWidth() == 0 ? 1 : clipData.getOutWidth());
    outHeight.set(clipData.getOutHeight() == 0 ? 1 : clipData.getOutHeight());
    alpha.set((int) (clipData.getAlpha() * 255));
    if (clipData.getScaleH() > scaleH.max()) {
      scaleH.set((int) scaleH.max());
    } else {
      scaleH.set(clipData.getScaleH());
    }

    if (clipData.getScaleL() > scaleV.max()) {
      scaleV.set((int) scaleV.max());
    } else {
      scaleV.set(clipData.getScaleL());
    }
  }

  void setDefault() {
    // 下位机的限制，这几个值不能为0
    clipWidth.set(1);
    clipHeight.set(1);
    outWidth.set(1);
    outHeight.set(1);
  }


}
