/*
 * Javolution - Java(TM) Solution for Real-Time and Embedded Systems Copyright (C) 2012 - Javolution
 * (http://javolution.org/) All rights reserved.
 *
 * Permission to use, copy, modify, and distribute this software is freely granted, provided that
 * this notice is preserved.
 */

package com.mc.tool.caesar.api.io;

import java.io.CharConversionException;
import java.io.IOException;
import java.io.OutputStream;
import java.io.Writer;

/**
 * <p>
 * A UTF-8 stream writer.
 * </p>
 *
 * <p>
 * This writer supports surrogate <code>char</code> pairs (representing characters in the range
 * [U+10000 .. U+10FFFF]). It can also be used to write characters from their unicodes (31 bits)
 * directly (ref. {@link #write(int)}).
 * </p>
 *
 * <p>
 * Instances of this class can be reused for different output streams and can be part of a higher
 * level component (e.g. serializer) in order to avoid dynamic buffer allocation when the
 * destination output changes. Also wrapping using a <code>java.io.BufferedWriter</code> is
 * unnescessary as instances of this class embed their own data buffers.
 * </p>
 *
 * <p>
 * Note: This writer is unsynchronized and always produces well-formed UTF-8 sequences.
 * </p>
 *
 * <AUTHOR> href="mailto:<EMAIL>"><PERSON>-<PERSON> Dautelle</a>
 * @version 2.0, December 9, 2004
 */
public final class Utf8StreamWriter extends Writer {

  /**
   * Holds the current output stream or <code>null</code> if closed.
   */
  private OutputStream outputStream;

  /**
   * Holds the bytes' buffer.
   */
  private final byte[] bytes;

  /**
   * Holds the bytes buffer index.
   */
  private int index;

  /**
   * Creates a UTF-8 writer having a byte buffer of moderate capacity (2048).
   */
  public Utf8StreamWriter() {
    bytes = new byte[2048];
  }

  /**
   * Creates a UTF-8 writer having a byte buffer of moderate capacity (2048), initialized to use the
   * provided Output Stream.
   *
   * @param ops The OutputStream to Write With
   */
  public Utf8StreamWriter(OutputStream ops) {
    bytes = new byte[2048];
    outputStream = ops;
  }

  /**
   * Creates a UTF-8 writer having a byte buffer of specified capacity.
   *
   * @param capacity the capacity of the byte buffer.
   */
  public Utf8StreamWriter(int capacity) {
    bytes = new byte[capacity];
  }

  /**
   * Creates a UTF-8 writer having a byte buffer of specified capacity, initialized to use the
   * provided Output Stream.
   *
   * @param ops      The OutputStream to Write With
   * @param capacity the capacity of the byte buffer.
   */
  public Utf8StreamWriter(OutputStream ops, int capacity) {
    bytes = new byte[capacity];
    outputStream = ops;
  }

  protected OutputStream getOutput() {
    return outputStream;
  }

  /**
   * Sets the output stream to use for writing until this writer is closed. For example:[code]
   * Writer writer = new UTF8StreamWriter().setOutputStream(out); [/code] is equivalent but writes
   * faster than [code] Writer writer = new java.io.OutputStreamWriter(out, "UTF-8"); [/code]
   *
   * @param out the output stream.
   * @return this UTF-8 writer.
   * @throws IllegalStateException if this writer is being reused and it has not been {@link #close
   *                               closed} or {@link #reset reset}.
   */
  public Utf8StreamWriter setOutput(OutputStream out) {
    if (outputStream != null) {
      throw new IllegalStateException("Writer not closed or reset");
    }
    outputStream = out;
    return this;
  }

  /**
   * Writes a single character. This method supports 16-bits character surrogates.
   *
   * @param cc <code>char</code> the character to be written (possibly a surrogate).
   * @throws IOException if an I/O error occurs.
   */
  public void write(char cc) throws IOException {
    if (outputStream == null) {
      throw new IOException("Writer closed");
    }
    if (cc < 0xd800 || cc > 0xdfff) {
      write((int) cc);
    } else if (cc < 0xdc00) { // High surrogate.
      highSurrogate = cc;
    } else { // Low surrogate.
      int code = ((highSurrogate - 0xd800) << 10) + (cc - 0xdc00) + 0x10000;
      write(code);
    }
  }

  private char highSurrogate;

  /**
   * Writes a character given its 31-bits Unicode.
   *
   * @param code the 31 bits Unicode of the character to be written.
   * @throws IOException if an I/O error occurs.
   */
  @Override
  public void write(int code) throws IOException {
    if (outputStream == null) {
      throw new IOException("Writer closed");
    }
    if ((code & 0xffffff80) == 0) {
      bytes[index] = (byte) code;
      if (++index >= bytes.length) {
        flushBuffer();
      }
    } else { // Writes more than one byte.
      write2(code);
    }
  }

  /**
   * Writes a portion of an array of characters.
   *
   * @param cbuf the array of characters.
   * @param off  the offset from which to start writing characters.
   * @param len  the number of characters to write.
   * @throws IOException if an I/O error occurs.
   */
  @Override
  public void write(char[] cbuf, int off, int len) throws IOException {
    if (outputStream == null) {
      throw new IOException("Writer closed");
    }
    final int off_plus_len = off + len;
    for (int i = off; i < off_plus_len; ) {
      char cc = cbuf[i++];
      if (cc < 0x80) {
        bytes[index] = (byte) cc;
        if (++index >= bytes.length) {
          flushBuffer();
        }
      } else {
        write(cc);
      }
    }
  }

  /**
   * Writes a portion of a string.
   *
   * @param str a String.
   * @param off the offset from which to start writing characters.
   * @param len the number of characters to write.
   * @throws IOException if an I/O error occurs
   */
  @Override
  public void write(String str, int off, int len) throws IOException {
    if (outputStream == null) {
      throw new IOException("Writer closed");
    }
    final int off_plus_len = off + len;
    for (int i = off; i < off_plus_len; ) {
      char cc = str.charAt(i++);
      if (cc < 0x80) {
        bytes[index] = (byte) cc;
        if (++index >= bytes.length) {
          flushBuffer();
        }
      } else {
        write(cc);
      }
    }
  }

  /**
   * Writes the specified character sequence.
   *
   * @param csq the character sequence.
   * @throws IOException if an I/O error occurs
   */
  public void write(CharSequence csq) throws IOException {
    if (outputStream == null) {
      throw new IOException("Writer closed");
    }
    final int length = csq.length();
    for (int i = 0; i < length; ) {
      char cc = csq.charAt(i++);
      if (cc < 0x80) {
        bytes[index] = (byte) cc;
        if (++index >= bytes.length) {
          flushBuffer();
        }
      } else {
        write(cc);
      }
    }
  }

  private void write2(int cc) throws IOException {
    if ((cc & 0xfffff800) == 0) { // 2 bytes.
      bytes[index] = (byte) (0xc0 | (cc >> 6));
      if (++index >= bytes.length) {
        flushBuffer();
      }
      bytes[index] = (byte) (0x80 | (cc & 0x3f));
      if (++index >= bytes.length) {
        flushBuffer();
      }
    } else if ((cc & 0xffff0000) == 0) { // 3 bytes.
      bytes[index] = (byte) (0xe0 | (cc >> 12));
      if (++index >= bytes.length) {
        flushBuffer();
      }
      bytes[index] = (byte) (0x80 | ((cc >> 6) & 0x3f));
      if (++index >= bytes.length) {
        flushBuffer();
      }
      bytes[index] = (byte) (0x80 | (cc & 0x3f));
      if (++index >= bytes.length) {
        flushBuffer();
      }
    } else if ((cc & 0xff200000) == 0) { // 4 bytes.
      bytes[index] = (byte) (0xf0 | (cc >> 18));
      if (++index >= bytes.length) {
        flushBuffer();
      }
      bytes[index] = (byte) (0x80 | ((cc >> 12) & 0x3f));
      if (++index >= bytes.length) {
        flushBuffer();
      }
      bytes[index] = (byte) (0x80 | ((cc >> 6) & 0x3f));
      if (++index >= bytes.length) {
        flushBuffer();
      }
      bytes[index] = (byte) (0x80 | (cc & 0x3f));
      if (++index >= bytes.length) {
        flushBuffer();
      }
    } else if ((cc & 0xf4000000) == 0) { // 5 bytes.
      bytes[index] = (byte) (0xf8 | (cc >> 24));
      if (++index >= bytes.length) {
        flushBuffer();
      }
      bytes[index] = (byte) (0x80 | ((cc >> 18) & 0x3f));
      if (++index >= bytes.length) {
        flushBuffer();
      }
      bytes[index] = (byte) (0x80 | ((cc >> 12) & 0x3f));
      if (++index >= bytes.length) {
        flushBuffer();
      }
      bytes[index] = (byte) (0x80 | ((cc >> 6) & 0x3f));
      if (++index >= bytes.length) {
        flushBuffer();
      }
      bytes[index] = (byte) (0x80 | (cc & 0x3f));
      if (++index >= bytes.length) {
        flushBuffer();
      }
    } else if ((cc & 0x80000000) == 0) { // 6 bytes.
      bytes[index] = (byte) (0xfc | (cc >> 30));
      if (++index >= bytes.length) {
        flushBuffer();
      }
      bytes[index] = (byte) (0x80 | ((cc >> 24) & 0x3f));
      if (++index >= bytes.length) {
        flushBuffer();
      }
      bytes[index] = (byte) (0x80 | ((cc >> 18) & 0x3f));
      if (++index >= bytes.length) {
        flushBuffer();
      }
      bytes[index] = (byte) (0x80 | ((cc >> 12) & 0x3F));
      if (++index >= bytes.length) {
        flushBuffer();
      }
      bytes[index] = (byte) (0x80 | ((cc >> 6) & 0x3F));
      if (++index >= bytes.length) {
        flushBuffer();
      }
      bytes[index] = (byte) (0x80 | (cc & 0x3F));
      if (++index >= bytes.length) {
        flushBuffer();
      }
    } else {
      throw new CharConversionException("Illegal character U+" + Integer.toHexString(cc));
    }
  }

  /**
   * Flushes the stream. If the stream has saved any characters from the various write() methods in
   * a buffer, write them immediately to their intended destination. Then, if that destination is
   * another character or byte stream, flush it. Thus one flush() invocation will flush all the
   * buffers in a chain of Writers and OutputStreams.
   *
   * @throws IOException if an I/O error occurs.
   */
  @Override
  public void flush() throws IOException {
    flushBuffer();
    outputStream.flush();
  }

  /**
   * Closes and {@link #reset resets} this writer for reuse.
   *
   * @throws IOException if an I/O error occurs
   */
  @Override
  public void close() throws IOException {
    if (outputStream != null) {
      flushBuffer();
      outputStream.close();
      reset();
    }
  }

  /**
   * Flushes the internal bytes buffer.
   *
   * @throws IOException if an I/O error occurs
   */
  private void flushBuffer() throws IOException {
    if (outputStream == null) {
      throw new IOException("Stream closed");
    }
    outputStream.write(bytes, 0, index);
    index = 0;
  }

  /**
   * .
   */
  public void reset() {
    highSurrogate = 0;
    index = 0;
    outputStream = null;
  }

  /**
   * setOutputStream.
   *
   * @param out OutpuStream to use with this writer.
   * @return Reference to this UTF8StreamWriter
   * @deprecated Replaced by {@link #setOutput(OutputStream)}
   */
  public Utf8StreamWriter setOutputStream(OutputStream out) {
    return this.setOutput(out);
  }
}
