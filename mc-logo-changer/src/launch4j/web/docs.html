<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html>
	<head>
		<title>Launch4j - Cross-platform Java executable wrapper</title>
		<meta name="description" content="Cross-platform Java executable wrapper for creating lightweight Windows native EXEs. Provides advanced JRE search, application startup configuration and better user experience.">
		<meta name="keywords" content="java executable wrapper, java application wrapper, exe wrapper, jar wrapper, wrap, wraps, wrapping, free software, launch, launcher, linux, mac, windows, open source, ant, native splash screen, deploy, build tool">
		<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
		<meta name="author" content="Grzegorz Kowal" >
		<link rel="stylesheet" type="text/css" href="style.css">
	</head>
	<body>
		<div id="container">
			<div id="top">
				<img style="width: 249px; height: 58px;" src="launch4j.gif" alt="launch4j"> <span class="version">3.12</span>
			</div>
			<div id="leftnav">
				<ul>
					<li><a href="index.html">Home</a></li>
					<li><a href="docs.html">Docs</a></li>
					<li><a href="changelog.html">Changelog</a></li>
					<li><a href="http://sourceforge.net/project/screenshots.php?group_id=95944">Screenshots</a></li>
					<li><a href="http://sourceforge.net/projects/launch4j/files/launch4j-3/3.12">Download</a></li>
					<li><a href="http://sourceforge.net/projects/launch4j/support">Support</a></li>
					<li><a href="http://sourceforge.net/projects/launch4j">Project summary</a></li>
					<li><a href="http://sourceforge.net/tracker/?atid=613100&amp;group_id=95944">Bug tracker</a></li>
					<li><a href="links.html">Links</a></li>
				</ul>
				<br />
				<a class="button" href="https://sourceforge.net/projects/launch4j/files/launch4j-3/3.12" rel="nofollow"><img alt="Downloads" src="https://img.shields.io/sourceforge/dm/launch4j.svg"></a>
				<a class="button" href="https://sourceforge.net/p/launch4j/" rel="nofollow"><img alt="Download Launch4j" src="https://sourceforge.net/sflogo.php?type=10&group_id=95944"></a>
			</div>
			<div id="content">
<a href="#Running_launch4j">Running launch4j</a><br>
<a href="#Configuration_file">Configuration file</a><br>
<a href="#Importing_1.x_configuration">Importing 1.x configuration</a><br>
<a href="#Ant_task">Ant Task</a><br>
<a href="#Additional_jvm_options">Additional JVM options at runtime</a><br>
<a href="#Runtime_options">Runtime options</a><br>
<a href="#Settings">Settings</a><br>

<h2 id="Running_launch4j">Running launch4j</h2>
Run <em>launch4j.exe</em> or <em>launch4j</em> script without command
line arguments to enter the GUI mode.

<pre>launch4j.exe</pre>

To wrap a jar in console mode use <em>launch4jc.exe</em>&nbsp;<em></em>and
specify the configuration file.

<pre>launch4jc.exe config.xml</pre>

On Linux use the <em>launch4j</em> script.

<pre>launch4j ./demo/l4j/config.xml</pre>

<h2 id="Configuration_file">Configuration file</h2>
Launch4j requires an xml configuration file for each output executable.
You can create and edit it conveniently using the graphic user
interface or your favorite editor. Alternatively it's possible to pass
all of the configuration parameters through the Ant task. All files
may be absolute paths or relative to the configuration file path.

<pre>
&lt;!--
  <strong>Bold</strong> elements are required.
  <span class="codeword"><u>Underlined</u></span> values are the default when the element is not specified.
  %VAR% expands environment/special variables and registry keys.
--&gt;
<strong>&lt;launch4jConfig&gt;</strong>
  <strong>&lt;headerType&gt;</strong><span class="codeword">gui|console|jniGui32|jniConsole32</span><strong>&lt;/headerType&gt;</strong>
  <strong>&lt;outfile&gt;</strong><span class="codeword">file.exe</span><strong><span class="codeword"></span>&lt;/outfile&gt;</strong>
  &lt;jar&gt;<span class="codeword">file</span><span class="codeword"></span>&lt;/jar&gt;
  &lt;dontWrapJar&gt;<span class="codeword">true|<u>false</u></span>&lt;/dontWrapJar&gt;
  &lt;errTitle&gt;<span class="codeword">application name</span>&lt;/errTitle&gt;
  &lt;downloadUrl&gt;<span class="codeword"><u>http://java.com/download</u></span>&lt;/downloadUrl&gt;
  &lt;supportUrl&gt;<span class="codeword">url</span>&lt;/supportUrl&gt;
  &lt;cmdLine&gt;<span class="codeword">text</span>&lt;/cmdLine&gt;
  &lt;chdir&gt;<span class="codeword">path</span>&lt;/chdir&gt;
  &lt;priority&gt;<span class="codeword"><u>normal</u>|idle|high</span>&lt;/priority&gt;
  &lt;stayAlive&gt;<span class="codeword">true|false</span>&lt;/stayAlive&gt;
  &lt;restartOnCrash&gt;<span class="codeword">true|false</span>&lt;/restartOnCrash&gt;
  &lt;icon&gt;<span class="codeword">file</span>&lt;/icon&gt;
  &lt;obj&gt;<span class="codeword">header object file</span>&lt;/obj&gt;
  ...
  &lt;lib&gt;<span class="codeword">w32api lib</span>&lt;/lib&gt;
  ...
  &lt;var&gt;<span class="codeword">var=text (%VAR%)</span>&lt;/var&gt;
  ...
  &lt;classPath&gt;
    <strong>&lt;mainClass&gt;</strong><span class="codeword">main class</span><strong>&lt;/mainClass&gt;</strong>
    <strong>&lt;cp&gt;</strong><span class="codeword">classpath (%VAR%)</span><strong>&lt;/cp&gt;</strong>
    ...
  &lt;/classPath&gt;
  &lt;singleInstance&gt;
    <strong>&lt;mutexName&gt;</strong><span class="codeword">text</span><strong>&lt;/mutexName&gt;</strong>
    &lt;windowTitle&gt;<span class="codeword">text</span>&lt;/windowTitle&gt;
  &lt;/singleInstance&gt; 
  <strong>&lt;jre&gt;</strong>
    &lt;!-- Specify <strong>path</strong> or <strong>minVersion</strong> or both. --&gt;
    <strong>&lt;path&gt;</strong><span class="codeword">bundled JRE path (%VAR%)</span><strong>&lt;/path&gt;</strong>
    &lt;bundledJre64Bit&gt;<span class="codeword">true|<u>false</u></span>&lt;/bundledJre64Bit&gt;
    &lt;bundledJreAsFallback&gt;<span class="codeword">true|<u>false</u></span>&lt;/bundledJreAsFallback&gt;
    <strong>&lt;minVersion&gt;</strong><span class="codeword">1.x.x[_xxx]|xxx[.xxx[.xxx]]</span><strong>&lt;/minVersion&gt;</strong>
    &lt;maxVersion&gt;<span class="codeword">1.x.x[_xxx]|xxx[.xxx[.xxx]]</span>&lt;/maxVersion&gt;
    &lt;jdkPreference&gt;<span class="codeword">jreOnly|<u>preferJre</u>|preferJdk|jdkOnly</span>&lt;/jdkPreference&gt;
    &lt;runtimeBits&gt;<span class="codeword">64|<u>64/32</u>|32/64|32</span>&lt;/runtimeBits&gt;
    &lt;!-- Heap sizes in MB and % of available memory. --&gt;
    &lt;initialHeapSize&gt;<span class="codeword">MB</span>&lt;/initialHeapSize&gt;
    &lt;initialHeapPercent&gt;<span class="codeword">%</span>&lt;/initialHeapPercent&gt;
    &lt;maxHeapSize&gt;<span class="codeword">MB</span>&lt;/maxHeapSize&gt;
    &lt;maxHeapPercent&gt;<span class="codeword">%</span>&lt;/maxHeapPercent&gt;
    &lt;opt&gt;<span class="codeword">text (%VAR%)</span>&lt;/opt&gt;
    ...
  <strong>&lt;/jre&gt;</strong>
  &lt;splash&gt;
    <strong>&lt;file&gt;</strong><span class="codeword">file</span><strong>&lt;/file&gt;</strong>
    &lt;waitForWindow&gt;<span class="codeword"><u>true</u>|false</span>&lt;/waitForWindow&gt;
    &lt;timeout&gt;<span class="codeword">seconds [60]</span>&lt;/timeout&gt;
    &lt;timeoutErr&gt;<span class="codeword"><u>true</u>|false</span>&lt;/timeoutErr&gt;
  &lt;/splash&gt;
  &lt;versionInfo&gt;
    <strong>&lt;fileVersion&gt;</strong><span class="codeword">x.x.x.x</span><strong>&lt;/fileVersion&gt;</strong>
    <strong>&lt;txtFileVersion&gt;</strong><span class="codeword">text</span><strong>&lt;/txtFileVersion&gt;</strong>
    <strong>&lt;fileDescription&gt;</strong><span class="codeword">text</span><strong>&lt;/fileDescription&gt;</strong>
    <strong>&lt;copyright&gt;</strong><span class="codeword">text</span><strong>&lt;/copyright&gt;</strong>
    <strong>&lt;productVersion&gt;</strong><span class="codeword">x.x.x.x</span><strong>&lt;/productVersion&gt;</strong>
    <strong>&lt;txtProductVersion&gt;</strong><span class="codeword">text</span><strong>&lt;/txtProductVersion&gt;</strong>
    <strong>&lt;productName&gt;</strong><span class="codeword">text</span><strong>&lt;/productName&gt;</strong>
    &lt;companyName&gt;<span class="codeword">text</span>&lt;/companyName&gt;
    <strong>&lt;internalName&gt;</strong><span class="codeword">filename</span><strong>&lt;/internalName&gt;</strong>
    <strong>&lt;originalFilename&gt;</strong><span class="codeword">filename.exe</span><strong>&lt;/originalFilename&gt;</strong>
    &lt;trademarks&gt;<span class="codeword">text</span>&lt;/trademarks&gt;
    &lt;language&gt;<span class="codeword">
      ALBANIAN|ARABIC|BAHASA|DUTCH_BELGIAN|FRENCH_BELGIAN|BULGARIAN|
      FRENCH_CANADIAN|CASTILIAN_SPANISH|CATALAN|CROATO_SERBIAN_LATIN|
      CZECH|DANISH|DUTCH|ENGLISH_UK|<u>ENGLISH_US</u>|FINNISH|FRENCH|GERMAN|
      GREEK|HEBREW|HUNGARIAN|ICELANDIC|ITALIAN|JAPANESE|KOREAN|
      NORWEGIAN_BOKMAL|NORWEGIAN_NYNORSK|POLISH|PORTUGUESE_BRAZIL|
      PORTUGUESE_PORTUGAL|RHAETO_ROMANIC|ROMANIAN|RUSSIAN|
      SERBO_CROATIAN_CYRILLIC|SIMPLIFIED_CHINESE|SLOVAK|SPANISH_MEXICO|
      SWEDISH|FRENCH_SWISS|GERMAN_SWISS|ITALIAN_SWISS|THAI|
      TRADITIONAL_CHINESE|TURKISH|URDU
    </span>&lt;/language&gt;
  &lt;/versionInfo&gt;
  &lt;messages&gt;
    <strong>&lt;startupErr&gt;</strong>text<strong>&lt;/startupErr&gt;</strong>
    <strong>&lt;bundledJreErr&gt;</strong>text<strong>&lt;/bundledJreErr&gt;</strong>
    <strong>&lt;jreVersionErr&gt;</strong>text<strong>&lt;/jreVersionErr&gt;</strong>
    <strong>&lt;launcherErr&gt;</strong>text<strong>&lt;/launcherErr&gt;</strong>
    &lt;!-- Used by console header only. --&gt;
    <strong>&lt;instanceAlreadyExistsMsg&gt;</strong>text<strong>&lt;/instanceAlreadyExistsMsg&gt;</strong>
  &lt;/messages&gt;
<strong>&lt;/launch4jConfig&gt;</strong>
</pre>

<dl>
	<dt>&lt;headerType&gt;</dt>
	<dd>
		Type of the header used to wrap the application.
		<table>
			<thead>
		    	<tr>
		    		<th>Header type</th>
		    		<th>Launcher</th>
		    		<th>Splash screen</th>
		    		<th style="white-space: nowrap">Wait for the application to close</th>
		    	</tr>
			</thead>
			<tbody>
		    	<tr>
		    		<td>gui</td>
		    		<td>javaw</td>
		    		<td>yes</td>
		    		<td class="description">wrapper waits only if <em>stayAlive</em> is set to true,
		    			otherwise it terminates immediately or after closing
		    			the splash screen.
		    		</td>
		    	</tr>
		    	<tr>
		    		<td>console</td>
		    		<td>java</td>
		    		<td>no</td>
		    		<td class="description">always waits and returns application's exit code.</td>
		    	</tr>
		    	<tr>
		    		<td>jniGui32 <strong style="color: red">(beta)</strong></td>
		    		<td>launch4j</td>
		    		<td>yes</td>
		    		<td class="description">always waits and returns application's exit code.</td>
		    	</tr>
		    	<tr>
		    		<td>jniConsole32 <strong style="color: red">(beta)</strong></td>
		    		<td>launch4j</td>
		    		<td>no</td>
		    		<td class="description">always waits and returns application's exit code.</td>
		    	</tr>
		    </tbody>
		</table>
		<br />
		When JNI headers are used the JVM is created directly by the launch4j wrapper executable instead of running java/javaw launchers.
		The process name will be the same as the wrapper. The JNI headers are in BETA and some features are not available which is signaled 
		during wrapper build based on the configuration.
		For production use consider the classic gui/console headers.
		<br />
		Missing features:
		<ul>
			<li>Only '.' is allowed in change directory (chdir).</li>
			<li>Command line arguments cannot be passed to the executable.</li>
			<li>Constant command line arguments (cmdLine).</li>
			<li>stayAlive should be ignored by JNI headers.</li>
			<li>Restart of appplication based on exit code (restartOnCrash).</li>
			<li>Process priority - only normal is supported (priority).</li>
			<li>Jar manifest is ignored, mainclass and classpath must be defined.</li>
			<li>64-bit JRE are not supported.</li>
		</ul>
	</dd>
</dl>
<dl>
	<dt>&lt;outfile&gt;</dt>
	<dd>Output executable file.</dd>
</dl>
<dl>
	<dt>&lt;jar&gt;</dt>
	<dd>
		Optional, by default specifies the jar to wrap. To launch a jar without
		wrapping it enter the runtime path of the jar relative to
		the executable and set <em>&lt;dontWrapJar&gt;</em> to true.
		For example, if the executable launcher and the application jar named
		<i>calc.exe</i> and <i>calc.jar</i> are in the same directory
		then you would use <i>&lt;jar&gt;calc.jar&lt;/jar&gt;</i>
		and <em>&lt;dontWrapJar&gt;true&lt;/dontWrapJar&gt;</em>.
	</dd>
</dl>
<dl>
	<dt>&lt;dontWrapJar&gt;</dt>
	<dd>
		Optional, defaults to false. Launch4j by default wraps jars in native
		executables, you can prevent this by setting <em>&lt;dontWrapJar&gt;</em> to true.
		The exe acts then as a launcher and starts the application specified in
		<em>&lt;jar&gt;</em> or <em>&lt;classPath&gt;&lt;mainClass&gt;</em>
	</dd>
</dl>
<dl>
	<dt>&lt;errTitle&gt;</dt>
	<dd>
		Optional, sets the title of the error message box that's displayed if Java cannot
		be found for instance. This usually should contain the name of your
		application. The console header prefixes error messages with this
		property (myapp: error...)
	</dd>
</dl>
<dl>
	<dt>&lt;cmdLine&gt;</dt>
	<dd>Optional, constant command line arguments.</dd>
</dl>
<dl>
	<dt>&lt;chdir&gt;</dt>
	<dd>Optional. Change current directory to an arbitrary path relative to the executable.
		If you omit this property or leave it blank it will have no effect.
		Setting it to <em>.</em> will change the current dir to the same directory
		as the executable. <em>..</em> will change it to the parent directory, and so on.
	</dd>
	<dd>
		<pre>&lt;chdir&gt;.&lt;/chdir&gt;</pre>
	</dd>
	<dd>
		<pre>&lt;chdir&gt;../somedir&lt;/chdir&gt;</pre>
	</dd>
</dl>
<dl>
	<dt>&lt;stayAlive&gt;</dt>
	<dd>Optional, defaults to false in GUI header, always true in console header.
		When enabled the launcher waits for the Java application
		to finish and returns it's exit code.
	</dd>
</dl>
<dl>
	<dt>&lt;restartOnCrash&gt;</dt>
	<dd>Optional, defaults to false.
		When the application exits, any exit code other than 0 is considered a crash and the application will be started again.
	</dd>
</dl>
<dl>
	<dt>&lt;icon&gt;</dt>
	<dd>Application icon in ICO format. May contain multiple color depths/resolutions.</dd>
</dl>
<dl>
	<dt>&lt;obj&gt;</dt>
	<dd>Optional, custom headers only. Ordered list of header object files.</dd>
</dl>
<dl>
	<dt>&lt;lib&gt;</dt>
	<dd>Optional, custom headers only. Ordered list of libraries used by header.</dd>
</dl>
<dl>
	<dt>&lt;singleInstance&gt;</dt>
	<dd>Optional, allow to run only a single instance of the application.</dd>
</dl>
<blockquote>
	<dl>
		<dt>&lt;mutexName&gt;</dt>
		<dd>Unique mutex name that will identify the application.</dd>
		<dt>&lt;windowTitle&gt;</dt>
		<dd>Optional, recognized by GUI header only. Title or title part of a window
			to bring up instead of running a new instance.
		</dd>
	</dl>
</blockquote>
<dl>
	<dt>&lt;jre&gt;</dt>
	<dd>Required element that groups JRE settings.</dd>
</dl>
<blockquote>
    <dl>
    	<dt>&lt;path&gt;, &lt;minVersion&gt;, &lt;maxVersion&gt;</dt>
        <dd>The <em>&lt;path&gt;</em> property is used
    		to specify the absolute or relative path (to the executable) of a bundled JRE, it
    		does not rely on the current directory or <em>&lt;chdir&gt;</em>.
    		Note that this path is not checked until the actual application execution.
    		If you'd like the wrapper to search for a JRE (public or SDK private)
    		use the <em>&lt;minVersion&gt;</em> property, you may also specify
    		the <em>&lt;maxVersion&gt;</em> to prevent it from using higher Java versions.
    		Launch4j will always use the highest version available (in the min/max range of course).
    		If an Oracle's JRE is not available or does not satisfy the search criteria,
    		the search will be repeated on IBM runtimes.
    		You can also combine these properties to change the startup process...
    	</dd>
	</dl>
	<blockquote>
        <dl>
        	<dt>&lt;path&gt;</dt>
        	<dd>Run if bundled JRE and javaw.exe are present, otherwise stop with error.</dd>
            <dt>&lt;path&gt; + &lt;minVersion&gt;&nbsp; [+ &lt;maxVersion&gt;]</dt>
            <dd>Use bundled JRE first, if it cannot be located search for Java,
        		if that fails display error message and open the Java download page.
        	</dd>
            <dt>&lt;minVersion&gt;&nbsp; [+ &lt;maxVersion&gt;]</dt>
            <dd>Search for Java, if an appropriate version cannot be found display
        		error message and open the Java download page.
        	</dd>
        </dl>
	</blockquote>
</blockquote>
<blockquote>
    <dl>
    	<dt>&lt;minVersion&gt;, &lt;maxVersion&gt; format - Java < 9</dt>
        <dd>The traditional version scheme supported by launch4j is 1.x.x[_xxx] and requires at least 3 parts, for example:
<pre>
1.6.0
1.7.0_51
1.8.0_121
</pre>
    	</dd>
	</dl>
</blockquote>
<blockquote>
    <dl>
    	<dt>&lt;minVersion&gt;, &lt;maxVersion&gt; format - Java >= 9</dt>
        <dd>The new version format is xxx[.xxx[.xxx]] where only 1 part is required and the update version after the underscore is not allowed.
<pre>
1.6
9
10.0.1
</pre>
    	</dd>
	</dl>
</blockquote>
<blockquote>
	<dl>
		<dt>&lt;bundledJre64Bit&gt;</dt>
		<dd>Optional, defaults to false which limits the calculated heap size to the 32-bit maximum.
			Set to true in order to use the available memory without this limit. This option works 
			in combination with the HeapSize and HeapPercent options only if the found JRE is a bundled one.
			In the standard JRE search based on registry the wrapper detects the type of JRE itself
			and uses the 32-bit heap limit when needed. 
		</dd>
	</dl>
</blockquote>
<blockquote>
	<dl>
		<dt>&lt;bundledJreAsFallback&gt;</dt>
		<dd>Optional, defaults to false which treats the bundled JRE as the primary runtime.
			When set to true, the bundled JRE will only be used in case the mix/max version search fails.
			This can be used as a fallback option if the user does not have the required Java installed
			and the bundled JRE	is provided on a CD or shared network location.
		</dd>
	</dl>
</blockquote>
<blockquote>
	<dl>
		<dt>&lt;jdkPreference&gt;</dt>
		<dd>Optional, defaults to preferJre; Allows you to specify a preference
			for a public JRE or a private JDK runtime. Valid values are:
		</dd>
	</dl>
	<blockquote>
		<dl>
			<dt>jreOnly</dt>
			<dd>Always use a public JRE (equivalent to the
				old option dontUsePrivateJres=true)</dd>
			<dt>preferJre</dt>
			<dd>Prefer a public JRE, but use a JDK private
				runtime if it is newer than the public
				JRE (equivalent to the old option
				dontUsePrivateJres=false)</dd>
			<dt>preferJdk</dt>
			<dd>Prefer a JDK private runtime, but use a
				public JRE if it is newer than the JDK
			</dd>
			<dt>jdkOnly</dt>
			<dd>Always use a private JDK runtime (fails
				if there is no JDK installed)
			</dd>
		</dl>
	</blockquote>
	<dl>
		<dt>&lt;runtimeBits&gt;</dt>
		<dd>Optional, defaults to 64/32; Allows to select between 64-bit and 32-bit runtimes.
			Valid values are:
		</dd>
	</dl>
	<blockquote>
		<dl>
			<dt>64</dt>
			<dd>Use only 64-bit runtimes</dd>
			<dt>64/32</dt>
			<dd>Use 64-bit runtimes if available, otherwise use 32-bit</dd>
			<dt>32/64</dt>
			<dd>Use 32-bit runtimes if available, otherwise use 64-bit</dd>
			<dt>32</dt>
			<dd>Use only 32-bit runtimes</dd>
		</dl>
	</blockquote>
	<dl>
		<dt>HeapSize, HeapPercent</dt>
		<dd>If size and percent are specified, then the setting which yields
			more memory will be chosen at runtime. In other words, setting both values
			means: percent of available memory no less than size in MB.
		</dd>
	</dl>
	<blockquote>
		<dl>
			<dt>&lt;initialHeapSize&gt;</dt>
			<dd>Optional, initial heap size in MB.</dd>
		</dl>
		<dl>
			<dt>&lt;initialHeapPercent&gt;</dt>
			<dd>Optional, initial heap size in % of available memory.</dd>
		</dl>
		<dl>
			<dt>&lt;maxHeapSize&gt;</dt>
			<dd>Optional, max heap size in MB.</dd>
		</dl>
		<dl>
			<dt>&lt;maxHeapPercent&gt;</dt>
			<dd>Optional, max heap size in % of available memory.</dd>
		</dl>	
	</blockquote>
	<dl>
		<dt>&lt;opt&gt;</dt>
		<dd>Optional, accepts everything you would normally pass to
			java/javaw launcher: assertion options, system properties and X options.
			Here you can map environment and special variables <em>EXEDIR</em>
			(exe's runtime directory), <em>EXEFILE</em> (exe's runtime full file path)
			to system properties. All variable references must be surrounded with
			percentage signs and quoted.
<pre>
&lt;opt&gt;-Dlaunch4j.exedir="%EXEDIR%"&lt;/opt&gt;
&lt;opt&gt;-Dlaunch4j.exefile="%EXEFILE%"&lt;/opt&gt;
&lt;opt&gt;-Denv.path="%Path%"&lt;/opt&gt;
&lt;opt&gt;-Dsettings="%HomeDrive%%HomePath%\\settings.ini"&lt;/opt&gt;
</pre>
		</dd>
	</dl>
</blockquote>

<dl>
	<dt>&lt;splash&gt;</dt>
	<dd>Optional, groups the splash screen settings. Allowed only in GUI header.</dd>
</dl>
<blockquote>
	<dl>
		<dt>&lt;file&gt;</dt>
		<dd>Splash screen image in BMP format.</dd>
	</dl>
	<dl>
		<dt>&lt;waitForWindow&gt;</dt>
		<dd>Optional, defaults to true. Close the splash screen when an application
			window or Java error message box appears. If set to false,
			the splash screen will be closed on timeout.
		</dd>
	</dl>
	<dl>
		<dt>&lt;timeout&gt;</dt>
		<dd>Optional, defaults to 60. Number of seconds after which the splash screen
			must be closed. Splash timeout may cause an error depending on
			<em>&lt;timeoutErr&gt;</em>.
		</dd>
	</dl>
	<dl>
		<dt>&lt;timeoutErr&gt;</dt>
		<dd>Optional, defaults to true. True signals an error on splash timeout,
			false closes the splash screen quietly.
		</dd>
	</dl>
</blockquote>

<dl>
	<dt>&lt;versionInfo&gt;</dt>
	<dd>Optional, <a href="https://msdn.microsoft.com/en-us/library/windows/desktop/aa381058%28v=vs.85%29.aspx">version information</a>
	to be displayed by the Windows Explorer.</dd>
</dl>
<blockquote>
	<dl>
		<dt>&lt;fileVersion&gt;</dt>
		<dd>Version number 'x.x.x.x'</dd>
	</dl>
	<dl>
		<dt>&lt;txtFileVersion&gt;</dt>
		<dd>Free form file version, for example '1.20.RC1'.</dd>
	</dl>
	<dl>
		<dt>&lt;fileDescription&gt;</dt>
		<dd>File description presented to the user.</dd>
	</dl>
	<dl>
		<dt>&lt;copyright&gt;</dt>
		<dd>Legal copyright.</dd>
	</dl>
	<dl>
		<dt>&lt;productVersion&gt;</dt>
		<dd>Version number 'x.x.x.x'</dd>
	</dl>
	<dl>
		<dt>&lt;txtProductVersion&gt;</dt>
		<dd>Free form file version, for example '1.20.RC1'.</dd>
	</dl>
	<dl>
		<dt>&lt;productName&gt;</dt>
		<dd>Text.</dd>
	</dl>
	<dl>
		<dt>&lt;companyName&gt;</dt>
		<dd>Optional text.</dd>
	</dl>
	<dl>
		<dt>&lt;internalName&gt;</dt>
		<dd>Internal name without extension, original filename or module name for example.</dd>
	</dl>
	<dl>
		<dt>&lt;originalFilename&gt;</dt>
		<dd>Original name of the file without the path. Allows to determine
			whether a file has been renamed by a user.
		</dd>
	</dl>
	<dl>
		<dt>&lt;trademarks&gt;</dt>
		<dd>Trademarks that apply to the file.</dd>
	</dl>
	<dl>
		<dt>&lt;language&gt;</dt>
		<dd>One of the language codes.</dd>
	</dl>
</blockquote>

<h2 id="Importing_1.x_configuration">Importing 1.x configuration</h2>
It's possible to import a 1.x configuration file using the GUI
interface. Open the file, correct the paths and save it as a new xml
configuration.

<h2 id="Ant_task">Ant task</h2>
You may set a launch4j directory property or change the task definition.

<pre>&lt;property name="launch4j.dir" location="/opt/launch4j" /&gt;</pre>

Define the task in your Ant build script.

<pre>
&lt;taskdef name="launch4j"
    classname="net.sf.launch4j.ant.Launch4jTask"
    classpath="${launch4j.dir}/launch4j.jar
        :${launch4j.dir}/lib/xstream.jar" /&gt;
</pre>

Execute the task!

<pre>&lt;launch4j configFile="./l4j/demo.xml" /&gt;</pre>

You can set or override the following configuration properties...
<p class="attrib">
	jar="absolute path or relative to <em>basedir</em>"<br>
	jarPath="relative path"<br>	
	outfile<br>
	fileVersion<br>
	txtFileVersion<br>
	productVersion<br>
	txtProductVersion<br>
	bindir="<a href="#Settings">alternate bin directory...</a>"<br>
	tmpdir="<a href="#Settings">alternate working directory...</a>"
</p>

<pre>
&lt;launch4j configFile="./l4j/demo.xml" outfile="mydemo.exe"
    fileVersion="*******" txtFileVersion="1.0 RC2" /&gt;
</pre>

You can also define the entire configuration in the task, but it will
not be possible to edit such a file in the GUI mode. All paths except
for <em>&lt;chdir&gt;</em>, <em>&lt;jre&gt;&lt;path&gt;</em> and <em>jarPath</em>
are calculated using the <em>basedir</em> project attribute.

<pre>
&lt;launch4j&gt;
  &lt;config headerType="gui" outfile="demo.exe"
      dontWrapJar="true" jarPath="demo.jar" &gt;
    &lt;var&gt;SETTINGS="%HomeDrive%%HomePath%\\settings.ini"&lt;/var&gt;
    &lt;classPath mainClass="org.demo.DemoApp"&gt;
        &lt;cp&gt;./lib/looks.jar&lt;/cp&gt;
        &lt;cp&gt;%USER_LIBS%/*.jar&lt;/cp&gt;
    &lt;/classPath&gt;
    &lt;jre minVersion="1.4.0"&gt;
        &lt;opt&gt;-Dlaunch4j.exedir="%EXEDIR%"&lt;/opt&gt;
        &lt;opt&gt;-Dlaunch4j.exefile="%EXEFILE%"&lt;/opt&gt;
    &lt;/jre&gt;
  &lt;/config&gt;
&lt;/launch4j&gt;
</pre>

<h2 id="Additional_jvm_options">Additional JVM options at runtime</h2>
When you create a wrapper or launcher all configuration details are compiled into the
executable and cannot be changed without recreating it or hacking with a resource editor.
Launch4j 2.1.2 introduces a new feature that allows to pass additional JVM options
at runtime from an .l4j.ini file. Now you can specify the options in the configuration file,
ini file or in both, but you cannot override them. The ini file's name must correspond
to the executable's <em>(myapp.exe : myapp.l4j.ini)</em>.
The arguments should be separated with spaces or new lines, environment variable
expansion is supported, for example:
<pre>
# Launch4j runtime config
-Dswing.aatext=true
-Dsomevar="%SOMEVAR%"
-Xms16m
</pre>

<h2>Environemnt variables</h2>
The following variables can be used in the configuration file (elements which contain %VAR%).
They are substitued during runtime, so for example the EXEDIR is the directory where the user installed the wrapped application.
<dl>
	<dt class="option">%VAR%</dt>
	<dd>
		Any environment variable, for example %HOMEPATH%.
	</dd>

	<dt class="option">%EXEDIR%</dt>
	<dd>
		Wrapper directory path.
	</dd>
	
	<dt class="option">%EXEFILE%</dt>
	<dd>
		Wrapper executable file path.
	</dd>
	
	<dt class="option">%PWD%</dt>
	<dd>
		Current directory, after chdir was applied.
	</dd>
	
	<dt class="option">%OLDPWD%</dt>
	<dd>
		Directory before chdir was applied.
	</dd>
	
	<dt class="option">%JREHOMEDIR%</dt>
	<dd>
		Path to the JRE which was selected for executing the application. 
	</dd>
	
	<dt class="option">%HKEY_...\...%</dt>
	<dd>
		Registry key value, e.g.<br />
		%HKEY_LOCAL_MACHINE\SOFTWARE\Microsoft\MemoryDiagnostic\LastScanTime%
	</dd>
</dl>

<h2 id="Runtime_options">Runtime options</h2>
<dl>
	<dt class="option">--l4j-debug</dt>
	<dd>
		To make sure the output executable is configured correctly you can use the
		debug launching mode to log various information to the launch4j.log file.
		Environment variable can also be used...<br />
		SET Launch4j=debug
	</dd>

	<dt class="option">--l4j-debug-all</dt>
	<dd>
		Same as above, but additionally log loaded resources (options stored during wrapper creation).
		Environment variable can also be used...<br />
		SET Launch4j=debug-all
	</dd>	
	
	<dt class="option">--l4j-dont-wait</dt>
	<dd>
		Disable the "stay alive" function.
	</dd>

	<dt class="option">--l4j-no-splash</dt>
	<dd>
		Disable the splash screen.
	</dd>

	<dt class="option">--l4j-no-splash-err</dt>
	<dd>
		Disable splash screen error on timeout, might be useful on very slow computers.
	</dd>
</dl>

<h2 id="Settings">Settings</h2>
These settings are only used during building of the launcher or wrapper, not during their execution.
<dl>
	<dt>Alternate bin directory: launch4j.bindir</dt>
	<dd>
		It's possible to override the default bin directory location which contains windres and ld
		tools using the <em>launch4j.bindir</em> system property. The property can have two forms:
		a path relative to Launch4j's directory (<em>altbin</em> for example) or an absolute path.
	<dd>

  <dt>Working directory: launch4j.tmpdir</dt>
  <dd>Change the working directory if the default path contains spaces which windres cannot handle.</dd>
</dl>
			</div>
			<div class="footer">
			    All trademarks mentioned are properties of their respective owners.<br />
            	Copyright &copy; 2005-2017 Grzegorz Kowal
            	<p style="margin-top: 0.5em">
            	    <a href="https://sourceforge.net/p/launch4j/" rel="nofollow"><img alt="Download Launch4j Executable Wrapper" src="https://sourceforge.net/sflogo.php?type=16&group_id=95944"></a>
            	</p>
			</div>
		</div>
	</body>
</html>
