body, table {
	font: 12px/20px <PERSON>erdana, Arial, Helvetica, sans-serif;
}


pre {
	padding: 8px;
	border: 1px dashed #999999;
	background-color: #f1f1f1;
	font: 13px/20px "Courier New", Courier, monospace;
}


.version {
	color: #307fe1;
	font-weight: bold;
}


.codeword {
	color: #3333ff;
}
.attrib {
	color: #404040;
}
.option {
	font-family: "Courier New", Courier, monospace;
	font-weight: bold;
}


dt {
	margin-top: 1.5em;
	color: #404040;
	font-size: 115%;
	border-bottom: 1px solid #cccccc;
}
dd {
	margin-left: 1em;
}


.warn, ul.changes em {
	color: #ff0000;
}


table {
	margin-top: 1em;
	padding: 0;
	border: 1px solid #999999;
	border-collapse: collapse;
	text-align: center;
}
table th {
	padding: 2px 4px;
	border: 1px solid #999999;
	background-color: #f1f1f1;
}
table td {
	padding: 2px 4px;
	border: 1px solid #999999;
}
.description {
	text-align: left;
}


#container {
	width: 90%;
	margin: 10px auto;
	border-width: 0;
	background-color: #ffffff;
}


#top {
	padding: 0.5em;
	background-color: #ffffff;
}
#top h1 {
	margin: 0;
	padding: 0;
}


#leftnav {
	float: left;
	width: 170px;
	margin: 0;
	padding: 0.5em;
	background-color: #ffffff;
}
#leftnav ul {
	margin: 0;
	padding: 0;
	border: none;
	list-style-type: none;
	font-size: 115%;
}
#leftnav a {
	width: 170px;
	height: 1.6em;
	line-height: 1.6em;
	display: block;
	padding-left: 0.2em;
}
#leftnav a:link, #leftnav a:visited {
	text-decoration: none;
	color: #666666;
}
#leftnav a:hover {
	background-color: #307fe1;
	color: #ffffff;
}

#leftnav a.button {
	display: inline;
}
#leftnav a.button:hover {
	background-color: transparent;
}


#content {
	max-width: 52em;
	margin-left: 190px;
	padding: 1em;
	border-left: 1px solid #cccccc;
	background-color: #ffffff;
}

#content ul {
	list-style-image: url('bullet.gif');
}

#content a:link {
	text-decoration: none;
	color: #307fe1;
}
#content a:visited {
	text-decoration: none;
	color: #307fe1;
}
#content a:hover {
	color: #307fe1;
	text-decoration: underline;
}

#content h2 {
	font-size: 150%;
}
#content h2:first-child {
	margin: 0 0 0.5em;
}


.footer {
	clear: both;
	margin: 0;
	padding: 0.5em;
	background-color: #ffffff;
	color: #333333;
	text-align: center;
	font-size: 90%;
}
