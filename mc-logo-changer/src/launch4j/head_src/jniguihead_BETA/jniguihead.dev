[Project]
FileName=jniguihead.dev
Name=jniguihead
UnitCount=7
Type=0
Ver=2
ObjFiles=
Includes="C:\Program Files (x86)\Java\jdk 1.4\include";"C:\Program Files (x86)\Java\jdk 1.4\include\win32"
Libs=
PrivateResource=
ResourceIncludes=
MakeIncludes=
Compiler=
CppCompiler=
Linker=-n_@@_
IsCpp=0
Icon=
ExeOutput=
ObjectOutput=..\..\head_jni_BETA
OverrideOutput=0
OverrideOutputName=jniguihead.exe
HostApplication=
Folders=
CommandLine=
UseCustomMakefile=0
CustomMakefile=Makefile.win
IncludeVersionInfo=0
SupportXPThemes=0
CompilerSet=0
CompilerSettings=000000d000000000000001000
LogOutput=
LogOutputEnabled=0

[Unit1]
FileName=jniguihead.c
CompileCpp=0
Folder=jniguihead_BETA
Compile=1
Link=1
Priority=1000
OverrideBuildCmd=0
BuildCmd=$(CC) -c jniguihead.c -o ../../head_jni_BETA/jniguihead.o $(CFLAGS)

[Unit2]
FileName=jniguihead.h
CompileCpp=0
Folder=jniguihead_BETA
Compile=1
Link=1
Priority=1000
OverrideBuildCmd=0
BuildCmd=

[VersionInfo]
Major=0
Minor=1
Release=1
Build=1
LanguageID=1033
CharsetID=1252
CompanyName=
FileVersion=*******
FileDescription=Developed using the Dev-C++ IDE
InternalName=
LegalCopyright=
LegalTrademarks=
OriginalFilename=
ProductName=
ProductVersion=
AutoIncBuildNr=0
SyncProduct=0

[Unit4]
FileName=..\head.h
CompileCpp=0
Folder=jniguihead_BETA
Compile=1
Link=1
Priority=1000
OverrideBuildCmd=0
BuildCmd=

[Unit6]
FileName=..\jnihead.c
CompileCpp=0
Folder=jniguihead_BETA
Compile=1
Link=1
Priority=1000
OverrideBuildCmd=0
BuildCmd=

[Unit3]
FileName=..\head.c
CompileCpp=0
Folder=jniguihead_BETA
Compile=1
Link=1
Priority=1000
OverrideBuildCmd=0
BuildCmd=$(CC) -c head.c -o ../../head/head.o $(CFLAGS)

[Unit5]
FileName=..\resource.h
CompileCpp=0
Folder=jniguihead_BETA
Compile=1
Link=1
Priority=1000
OverrideBuildCmd=0
BuildCmd=

[Unit7]
FileName=..\jnihead.h
CompileCpp=0
Folder=jniguihead_BETA
Compile=1
Link=1
Priority=1000
OverrideBuildCmd=0
BuildCmd=

