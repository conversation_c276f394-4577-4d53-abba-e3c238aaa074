#
#	Launch4j (http://launch4j.sourceforge.net/)
#	Cross-platform Java application wrapper for creating Windows native executables.
#
#	Copyright (c) 2004, 2015 <PERSON><PERSON><PERSON><PERSON>
#	All rights reserved.
#
#	Redistribution and use in source and binary forms, with or without modification,
#	are permitted provided that the following conditions are met:
#	
#	1. Redistributions of source code must retain the above copyright notice,
#	   this list of conditions and the following disclaimer.
#	
#	2. Redistributions in binary form must reproduce the above copyright notice,
#	   this list of conditions and the following disclaimer in the documentation
#	   and/or other materials provided with the distribution.
#	
#	3. Neither the name of the copyright holder nor the names of its contributors
#	   may be used to endorse or promote products derived from this software without
#	   specific prior written permission.
#	
#	THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS"
#	AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO,
#	THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE
#	ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE LIABLE
#	FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
#	(INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
#	LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED
#	AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY,
#	OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE
#	OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
#

Splash.splash.file=Splash file
Splash.splash.timeout=Splash timeout

Config.specify.output.exe=Specify output file with .exe extension.
Config.application.jar=Application jar
Config.application.jar.path=Specify runtime path of the jar relative to the executable.
Config.chdir.relative='chdir' must be a path relative to the executable.
Config.chdir.path='chdir' is now a path instead of a boolean, please check the docs.
Config.manifest=Manifest
Config.icon=Icon
Config.jar.arguments=Jar arguments
Config.error.title=Error title
Config.download.url=Download URL
Config.support.url=Support URL
Config.header.type=Header type
Config.splash.not.impl.by.console.hdr=Splash screen is not implemented by console header.
Config.variables=Environment variables
Config.variables.err=Environment variable assignment should have the form varname=[value][%varref%]...
Config.priority=Process priority

ClassPath.mainClass=Main class
ClassPath.or.jar=Specify runtime path of a jar or the classpath.
ClassPath.path=Classpath

VersionInfo.file.version=File version, should be 'x.x.x.x'
VersionInfo.txt.file.version=Free form file version
VersionInfo.file.description=File description
VersionInfo.copyright=Copyright
VersionInfo.product.version=Product version, should be 'x.x.x.x'
VersionInfo.txt.product.version=Free from product version
VersionInfo.product.name=Product name
VersionInfo.company.name=Company name
VersionInfo.trademarks=Trademarks
VersionInfo.internal.name=Internal name
VersionInfo.internal.name.not.exe=Internal name shouldn't have the .exe extension.
VersionInfo.original.filename=Original filename
VersionInfo.original.filename.exe=Original filename should end with the .exe extension.

Jre.min.version=Minimum JRE version should be 1.x.x[_xxx] or the Java 9 new version scheme xxx[.xxx[.xxx]], e.g., 1.5, 1.8.0_121, 10.0.1
Jre.max.version=Maximum JRE version should be 1.x.x[_xxx] or the Java 9 new version scheme xxx[.xxx[.xxx]], e.g., 1.5, 1.8.0_121, 10.0.1
Jre.specify.jre.min.version.or.path=Specify minimum JRE version and/or bundled JRE path.
Jre.bundled.path=Bundled JRE path
Jre.bundled.64bit.invalid=The bundled JRE 64-bit option can only be used if the JRE path is specified.
Jre.bundled.fallback.invalid=The bundled JRE as fallback option can only be used if the JRE path is specified.
Jre.specify.min.version=Specify minimum JRE version.
Jre.max.greater.than.min=Maximum JRE version must be greater than the minimum.\nTo use a certain JRE version, you may set the min/max range to [1.4.2 - 1.4.2_10] for example.
Jre.initial.and.max.heap=If you change the initial heap size please also specify the maximum size.
Jre.initial.heap=Initial heap size must be greater than 0, leave the field blank to use the JVM default.
Jre.max.heap=Maximum heap size cannot be less than the initial size, leave the field blank to use the JVM default.
Jre.initial.heap.percent=Initial heap %
Jre.max.heap.percent=Maximum heap %
Jre.jdkPreference=JDK preference
Jre.runtimeBits=Runtime bits
Jre.jvm.options=JVM arguments
Jre.jvm.options.unclosed.quotation=JVM arguments contain an unclosed quotation.
Jre.jvm.options.variable=Invalid environment variable reference.

Msg.startupErr=Startup error message
Msg.bundledJreErr=Bundled JRE error message
Msg.jreVersionErr=JRE version error message
Msg.launcherErr=Launcher error message

SingleInstance.mutexName=Mutex name
SingleInstance.windowTitle=Window title

Charset.ascii=7-bit ASCII
Charset.unicode=Unicode
Charset.multilingual=Multilingual
Charset.shift.jis=Japan (Shift JIS X-0208)
Charset.shift.ksc=Korea (Shift KSC 5601)
Charset.big5=Taiwan (Big5)
Charset.latin2=Latin-2 (Eastern European)
Charset.cyrillic=Cyrillic
Language.arabic=Arabic
Language.bulgarian=Bulgarian
Language.catalan=Catalan
Language.chinese.traditional=Chinese (Traditional)
Language.czech=Czech
Language.danish=Danish
Language.german=German
Language.english.us=English U.S.
Language.greek=Greek
Language.hebrew=Hebrew
Language.polish=Polish
Language.portuguese.brazil=Portuguese (Brazil)
Language.rhaeto.romanic=Rhaeto Romanic
Language.romanian=Romanian
Language.russian=Russian
Language.spanish.castilian=Spanish (Castilian)
Language.finnish=Finnish
Language.french=French
Language.hungarian=Hungarian
Language.icelandic=Icelandic
Language.italian=Italian
Language.japanese=Japanese
Language.korean=Korean
Language.dutch=Dutch
Language.norwegian.bokmal=Norwegian Bokmål
Language.croato.serbian.latin=Croato-Serbian (Latin)
Language.slovak=Slovak
Language.albanian=Albanian
Language.swedish=Swedish
Language.thai=Thai
Language.turkish=Turkish
Language.urdu=Urdu
Language.bahasa=Bahasa
Language.chinese.simplified=Chinese (Simplified)
Language.swiss.german=Swiss German
Language.english.uk=English U.K.
Language.spanish.mexico=Spanish (Mexico)
Language.belgian.french=Belgian French
Language.canadian.french=Canadian French
Language.swiss.italian=Swiss Italian
Language.belgian.dutch=Belgian Dutch
Language.norwegian.nynorsk=Norwegian Nynorsk
Language.portuguese.portugal=Portuguese (Portugal)
Language.serbo.croatian.cyrillic=Serbo-Croatian (Cyrillic)
Language.swiss.french=Swiss French
