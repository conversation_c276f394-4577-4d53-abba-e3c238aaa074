MinGW - Licensing Terms

Various pieces distributed with MinGW come with its own copyright and license:

Basic MinGW runtime
    MinGW base runtime package is uncopyrighted and placed in the public domain.
    This basically means that you can do what you want with the code.

w32api
    You are free to use, modify and copy this package.
    No restrictions are imposed on programs or object files compiled with this library.
    You may not restrict the the usage of this library.
    You may distribute this library as part of another package or as a modified package
    if and only if you do not restrict the usage of the portions consisting
    of this (optionally modified) library.
    If distributed as a modified package then this file must be included.

    This library is distributed in the hope that it will be useful,
    but WITHOUT ANY WARRANTY; without even the implied warranty
    of MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.

MinGW profiling code
    MinGW profiling code is distributed under the GNU General Public License. 

The development tools such as GCC, GDB, GNU Make, etc all covered by GNU General Public License.
