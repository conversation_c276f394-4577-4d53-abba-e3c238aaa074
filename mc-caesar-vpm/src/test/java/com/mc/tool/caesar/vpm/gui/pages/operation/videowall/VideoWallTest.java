package com.mc.tool.caesar.vpm.gui.pages.operation.videowall;

import com.mc.tool.caesar.vpm.GuiTestConstants;
import com.mc.tool.caesar.vpm.gui.AppGuiTestBase;
import com.mc.tool.caesar.vpm.pages.operation.CaesarOperationPageNew;
import com.mc.tool.caesar.vpm.pages.systemedit.datamodel.CaesarUsbTxTerminal;
import com.mc.tool.framework.operation.view.VideoSourceTree;
import java.io.File;
import java.io.IOException;
import java.util.Optional;
import javafx.scene.Node;
import org.junit.Assert;
import org.junit.Test;

/**
 * .
 */
public class VideoWallTest extends AppGuiTestBase {

  @Override
  public void beforeAll() {}

  @Test
  public void testUsbSource() {
    // USB TX不应该出现在信号源列表
    try {
      clickOn(GuiTestConstants.RESTORE_GRAPH);
      for (int i = 0; i < 10; i++) {
        clickOn(GuiTestConstants.ZOOM_OUT_GRAPH);
      }
      // 创建视频墙
      rightClickOn(GuiTestConstants.VP6_DEMO_NAME);
      Optional<Node> menu = lookup(GuiTestConstants.MENU_CREATE_VIDEO_WALL).tryQuery();
      Assert.assertTrue(menu.isPresent());
      clickOn(GuiTestConstants.MENU_CREATE_VIDEO_WALL);
      // 创建USB
      createUsbImpl(2, "33", 1);
      // 切换到视频墙页面
      clickOn("#" + CaesarOperationPageNew.NAME);
      Node tree = lookup(GuiTestConstants.VIDEO_SOURCE_TREE).query();
      if (tree instanceof VideoSourceTree) {
        Assert.assertFalse(
            traverseTree(
                ((VideoSourceTree) tree).getRoot(), (obj) -> obj instanceof CaesarUsbTxTerminal));
      } else {
        Assert.fail();
      }

      File tempFile =
          loadResourceStatus("com/mc/tool/caesar/vpm/update/single_36_hw1.2_sample.status");
      tempFile.delete();
    } catch (IOException exception) {
      exception.printStackTrace();
      Assert.fail("Expect no exception!");
    }
  }
}
