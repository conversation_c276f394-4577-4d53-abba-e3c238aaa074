package com.mc.tool.caesar.vpm.gui.pages.systemedit;

import com.mc.common.control.TextWrapper;
import com.mc.graph.interfaces.CellSkin;
import com.mc.tool.caesar.vpm.GuiTestConstants;
import com.mc.tool.caesar.vpm.gui.AppGuiTestBase;
import com.mc.tool.caesar.vpm.pages.systemedit.datamodel.CaesarConTerminal;
import com.mc.tool.caesar.vpm.pages.systemedit.datamodel.CaesarCpuTerminal;
import com.mc.tool.caesar.vpm.pages.systemedit.datamodel.CaesarGridLineTerminal;
import com.sun.javafx.scene.control.skin.ContextMenuContent.MenuItemContainer;
import java.io.File;
import java.io.IOException;
import javafx.scene.Node;
import org.junit.Assert;
import org.junit.Test;

/**
 * .
 */
public class GridLineTest extends AppGuiTestBase {

  @Override
  public void beforeAll() {}

  @Test
  public void testContextMenu() {

    try {
      clickOn(GuiTestConstants.RESTORE_GRAPH);
      for (int i = 0; i < 10; i++) {
        clickOn(GuiTestConstants.ZOOM_OUT_GRAPH);
      }

      Node gridLineTerminal = findTerminalNode(CaesarGridLineTerminal.class);
      rightClickOn(gridLineTerminal);

      // 只能组合
      Node menuGroup = lookup(GuiTestConstants.MENU_GROUP).query().getParent();
      for (Node node : (lookup((node) -> node instanceof MenuItemContainer).queryAll())) {
        MenuItemContainer container = (MenuItemContainer) node;
        if (node == menuGroup) {
          Assert.assertFalse(container.getItem().isDisable());
        } else {
          Assert.assertTrue(container.getItem().isDisable());
        }
      }

      File tempFile = loadResourceStatus("com/mc/tool/caesar/vpm/matrixgrid.status");
      tempFile.delete();
    } catch (IOException exception) {
      exception.printStackTrace();
      Assert.fail("Expect no exception!");
    }
  }

  @Test
  public void testChangeName() {

    try {
      clickOn(GuiTestConstants.RESTORE_GRAPH);
      for (int i = 0; i < 10; i++) {
        clickOn(GuiTestConstants.ZOOM_OUT_GRAPH);
      }

      // 修改名称
      Node gridLineTerminal = findTerminalNode(CaesarGridLineTerminal.class);
      Object userdata = gridLineTerminal.getUserData();
      doubleClickOn(gridLineTerminal);

      String newName = Math.random() + "";
      targetWindow(GuiTestConstants.DIALOG_SET_NAME).write(newName);

      targetWindow(GuiTestConstants.DIALOG_SET_NAME).clickOn(GuiTestConstants.BUTTON_CONFIRM);
      // 关闭页面
      clickOn(GuiTestConstants.BUTTON_LIST_MENU);
      clickOn(GuiTestConstants.MENU_CLOSE_ALL);

      // 重新打开
      System.setProperty("use-status-model", "false");
      File tempFile = loadResourceStatus("com/mc/tool/caesar/vpm/matrixgrid.status");
      loadFileStatus(tempFile);
      clickOn(GuiTestConstants.RESTORE_GRAPH);
      for (int i = 0; i < 10; i++) {
        clickOn(GuiTestConstants.ZOOM_OUT_GRAPH);
      }
      gridLineTerminal = findTerminalNode(CaesarGridLineTerminal.class);
      // 检查名称
      Node nameText = gridLineTerminal.lookup(GuiTestConstants.TEXT_TERMINAL_NAME);
      Assert.assertEquals(newName, ((TextWrapper) nameText).getText());

      tempFile.delete();

    } catch (IOException exception) {
      exception.printStackTrace();
      Assert.fail("Expect no exception!");
    }
  }

  @Test
  public void testHighLight() {
    try {
      // 加载文件
      File tempFile = loadResourceStatus("com/mc/tool/caesar/vpm/matrixgrid2.status");
      clickOn(GuiTestConstants.RESTORE_GRAPH);
      for (int i = 0; i < 10; i++) {
        clickOn(GuiTestConstants.ZOOM_OUT_GRAPH);
      }

      // 第一页 TX与grid line关联
      Node txNode = findTerminalNode(CaesarCpuTerminal.class);
      Node rxNode = findTerminalNode(CaesarConTerminal.class);
      Node gridLineNode = findTerminalNode(CaesarGridLineTerminal.class);

      CellSkin txCellSkin = (CellSkin) txNode.getUserData();
      CellSkin rxCellSkin = (CellSkin) rxNode.getUserData();
      CellSkin gridLineCellSkin = (CellSkin) gridLineNode.getUserData();
      Assert.assertFalse(txCellSkin.getCell().highLightProperty().get());
      Assert.assertFalse(rxCellSkin.getCell().highLightProperty().get());
      Assert.assertFalse(gridLineCellSkin.getCell().highLightProperty().get());
      // 点击TX，高亮级联线
      clickOn(txNode);
      Assert.assertTrue(txCellSkin.getCell().highLightProperty().get());
      Assert.assertFalse(rxCellSkin.getCell().highLightProperty().get());
      Assert.assertTrue(gridLineCellSkin.getCell().highLightProperty().get());
      // 点击RX
      clickOn(rxNode);
      Assert.assertFalse(txCellSkin.getCell().highLightProperty().get());
      Assert.assertTrue(rxCellSkin.getCell().highLightProperty().get());
      Assert.assertFalse(gridLineCellSkin.getCell().highLightProperty().get());
      // 点击gridline
      clickOn(gridLineNode);
      Assert.assertTrue(txCellSkin.getCell().highLightProperty().get());
      Assert.assertFalse(rxCellSkin.getCell().highLightProperty().get());
      Assert.assertTrue(gridLineCellSkin.getCell().highLightProperty().get());
      // 点击matrix
      Node matrix = findMatrixNode();
      clickOn(matrix);
      Assert.assertFalse(txCellSkin.getCell().highLightProperty().get());
      Assert.assertFalse(rxCellSkin.getCell().highLightProperty().get());
      Assert.assertFalse(gridLineCellSkin.getCell().highLightProperty().get());

      // 第二页 RX 与 grid line关联
      clickOn(GuiTestConstants.SWITCH_GRAPH);
      // 点击未选中的菜单项
      Node anotherPageMenu = lookupGraphAnotherPageMenu();
      clickOn(anotherPageMenu);

      clickOn(GuiTestConstants.RESTORE_GRAPH);
      for (int i = 0; i < 10; i++) {
        clickOn(GuiTestConstants.ZOOM_OUT_GRAPH);
      }

      rxNode = findTerminalNode(CaesarConTerminal.class);
      gridLineNode = findTerminalNode(CaesarGridLineTerminal.class);

      rxCellSkin = (CellSkin) rxNode.getUserData();
      gridLineCellSkin = (CellSkin) gridLineNode.getUserData();

      Assert.assertFalse(rxCellSkin.getCell().highLightProperty().get());
      Assert.assertFalse(gridLineCellSkin.getCell().highLightProperty().get());
      // 点击gridline，两个都高亮
      clickOn(gridLineNode);
      Assert.assertTrue(rxCellSkin.getCell().highLightProperty().get());
      Assert.assertTrue(gridLineCellSkin.getCell().highLightProperty().get());
      // 点击RX，两个都高亮
      clickOn(rxNode);
      Assert.assertTrue(rxCellSkin.getCell().highLightProperty().get());
      Assert.assertTrue(gridLineCellSkin.getCell().highLightProperty().get());

    } catch (IOException exception) {
      exception.printStackTrace();
      Assert.fail("Expect no exception!");
    }
  }
}
