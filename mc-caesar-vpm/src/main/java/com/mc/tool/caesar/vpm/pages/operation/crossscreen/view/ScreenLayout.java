package com.mc.tool.caesar.vpm.pages.operation.crossscreen.view;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import lombok.Getter;
import lombok.Setter;

/**
 * .
 */
public class ScreenLayout {
  private List<ScreenWindow> windows = new ArrayList<>();

  public Collection<ScreenWindow> getWindows() {
    return new ArrayList<>(windows);
  }

  /**
   * 获取第一个窗口.
   *
   * @return 第一个窗口
   */
  public ScreenWindow getFirstWindow() {
    if (windows.size() > 0) {
      return windows.get(0);
    } else {
      return null;
    }
  }

  public int getWindowCount() {
    return windows.size();
  }

  /**
   * 添加窗口.
   *
   * @param window 窗口
   */
  public void addWindow(ScreenWindow window) {
    if (window != null) {
      windows.add(window);
    }
  }

  /**
   * .
   */
  public static class ScreenWindow {
    @Getter @Setter private double width = 0;
    @Getter @Setter private double height = 0;
    @Getter @Setter private double xpos = 0;
    @Getter @Setter private double ypos = 0;
    @Getter @Setter private String name = "";

    @Override
    public int hashCode() {
      return (int) width ^ (int) height ^ (int) xpos ^ (int) ypos ^ name.hashCode();
    }

    @Override
    public boolean equals(Object obj) {
      if (obj instanceof ScreenWindow) {
        ScreenWindow input = (ScreenWindow) obj;
        boolean result;
        result = Math.abs(width - input.width) < .0000001;
        result &= Math.abs(height - input.height) < .0000001;
        result &= Math.abs(xpos - input.xpos) < .0000001;
        result &= Math.abs(ypos - input.ypos) < .0000001;
        result &= name.equals(input.name);
        return result;
      } else {
        return false;
      }
    }
  }
}
