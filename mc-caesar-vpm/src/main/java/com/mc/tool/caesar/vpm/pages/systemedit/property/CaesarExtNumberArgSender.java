package com.mc.tool.caesar.vpm.pages.systemedit.property;

import com.mc.tool.caesar.api.datamodel.ExtenderData;
import com.mc.tool.caesar.api.exception.BusyException;
import com.mc.tool.caesar.api.exception.ConfigException;
import com.mc.tool.caesar.api.exception.DeviceConnectionException;
import com.mc.tool.caesar.vpm.devices.CaesarDeviceController;
import lombok.extern.slf4j.Slf4j;

/**
 * .
 *
 * <AUTHOR>
 */
@Slf4j
public class CaesarExtNumberArgSender implements CaesarPropertySender<Number> {
  private final CaesarDeviceController controller;
  private final ExtenderData extenderData;
  private final String propertyName;
  private final int extArgId;

  /**
   * 外设整型值参数发送器.
   *
   * @param controller controller
   * @param extenderData extender
   * @param extArgId 参数id
   * @param propertyName extender的属性名
   */
  public CaesarExtNumberArgSender(
      CaesarDeviceController controller,
      ExtenderData extenderData,
      int extArgId,
      String propertyName) {
    this.controller = controller;
    this.extenderData = extenderData;
    this.extArgId = extArgId;
    this.propertyName = propertyName;
  }

  @Override
  public void sendValue(Number value) {
    controller.execute(
        () -> {
          extenderData.setProperty(propertyName, value);
          try {
            controller.getDataModel().setExtNumberArg(extenderData, extArgId, value.intValue());
          } catch (ConfigException | BusyException | DeviceConnectionException exc) {
            log.warn("Fail to set {} for {}", propertyName, extenderData.getName(), exc);
          }
        });
  }
}
