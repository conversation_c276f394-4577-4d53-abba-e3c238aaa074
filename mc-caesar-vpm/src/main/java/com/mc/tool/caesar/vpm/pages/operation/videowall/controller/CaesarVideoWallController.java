package com.mc.tool.caesar.vpm.pages.operation.videowall.controller;

import com.google.common.collect.Lists;
import com.google.gson.Gson;
import com.mc.common.util.CommonResource;
import com.mc.graph.canvas.PageCanvas;
import com.mc.graph.canvas.PageCanvas.PageText;
import com.mc.graph.canvas.PageCanvasUtility;
import com.mc.graph.interfaces.CellSkin;
import com.mc.graph.util.NodeUtil;
import com.mc.graph.util.SelectableNode;
import com.mc.tool.caesar.api.CaesarConstants;
import com.mc.tool.caesar.api.CaesarSwitchDataModel;
import com.mc.tool.caesar.api.SwitchMultiviewAllVideoChannel;
import com.mc.tool.caesar.api.datamodel.ConsoleData;
import com.mc.tool.caesar.api.datamodel.CpuData;
import com.mc.tool.caesar.api.datamodel.ScenarioData;
import com.mc.tool.caesar.api.datamodel.VideoWallGroupData;
import com.mc.tool.caesar.api.datamodel.vp.Vp6ConfigData;
import com.mc.tool.caesar.api.datamodel.vp.Vp6OutputData;
import com.mc.tool.caesar.api.datamodel.vp.Vp7ConfigData;
import com.mc.tool.caesar.api.datamodel.vp.VpConConfigData;
import com.mc.tool.caesar.api.datamodel.vp.VpConsoleData;
import com.mc.tool.caesar.api.exception.BusyException;
import com.mc.tool.caesar.api.exception.ConfigException;
import com.mc.tool.caesar.api.exception.DeviceConnectionException;
import com.mc.tool.caesar.api.utils.CpuDataStateWrapper;
import com.mc.tool.caesar.api.utils.ExtendedSwitchUtility;
import com.mc.tool.caesar.api.utils.Utilities;
import com.mc.tool.caesar.vpm.devices.CaesarDeviceController;
import com.mc.tool.caesar.vpm.pages.operation.videowall.datamodel.CaesarDataUtility;
import com.mc.tool.caesar.vpm.pages.operation.videowall.datamodel.CaesarOsdData;
import com.mc.tool.caesar.vpm.pages.operation.videowall.datamodel.CaesarOutputData;
import com.mc.tool.caesar.vpm.pages.operation.videowall.datamodel.CaesarScreenData;
import com.mc.tool.caesar.vpm.pages.operation.videowall.datamodel.CaesarTestFrameData;
import com.mc.tool.caesar.vpm.pages.operation.videowall.datamodel.CaesarTestFrameMode;
import com.mc.tool.caesar.vpm.pages.operation.videowall.datamodel.CaesarVideoData;
import com.mc.tool.caesar.vpm.pages.operation.videowall.datamodel.CaesarVideoWallData;
import com.mc.tool.caesar.vpm.pages.operation.videowall.datamodel.DraggedVideoSourceInfo;
import com.mc.tool.caesar.vpm.pages.operation.videowall.menu.MenuAudioRx;
import com.mc.tool.caesar.vpm.pages.operation.videowall.view.UploadBgImgView;
import com.mc.tool.caesar.vpm.pages.operation.videowall.vp.CaesarVpMatrix;
import com.mc.tool.caesar.vpm.pages.operation.videowall.vp.CaesarVpVideoWall;
import com.mc.tool.caesar.vpm.pages.systemedit.datamodel.CaesarCpuTerminal;
import com.mc.tool.caesar.vpm.pages.systemedit.datamodel.CaesarMatrix;
import com.mc.tool.caesar.vpm.pages.systemedit.datamodel.CaesarVideoWallFunc;
import com.mc.tool.caesar.vpm.pages.systemedit.datamodel.vp.VpGroup;
import com.mc.tool.caesar.vpm.util.CaesarI18nCommonResource;
import com.mc.tool.caesar.vpm.util.CaesarNamePredicate;
import com.mc.tool.caesar.vpm.util.TerminalUtility;
import com.mc.tool.caesar.vpm.util.vp.Vp6Processor;
import com.mc.tool.caesar.vpm.util.vp.Vp6WallCurrCfg;
import com.mc.tool.caesar.vpm.util.vp.Vp7Processor;
import com.mc.tool.caesar.vpm.util.vp.Vp7WallCurrCfg;
import com.mc.tool.framework.interfaces.DeviceControllable;
import com.mc.tool.framework.operation.videowall.controller.VideoWallChecker.VideoWallError;
import com.mc.tool.framework.operation.videowall.controller.VideoWallOperationController;
import com.mc.tool.framework.operation.videowall.datamodel.IVideoWallLayout;
import com.mc.tool.framework.operation.videowall.datamodel.LayoutData;
import com.mc.tool.framework.operation.videowall.datamodel.interfaces.ScreenObject;
import com.mc.tool.framework.operation.videowall.datamodel.interfaces.VideoObject;
import com.mc.tool.framework.operation.videowall.datamodel.interfaces.VideoWallObject;
import com.mc.tool.framework.operation.videowall.menu.MenuToBottom;
import com.mc.tool.framework.operation.videowall.menu.MenuToDown;
import com.mc.tool.framework.operation.videowall.menu.MenuToTop;
import com.mc.tool.framework.operation.videowall.menu.MenuToUp;
import com.mc.tool.framework.operation.videowall.view.MultiResolutionConfigView;
import com.mc.tool.framework.systemedit.datamodel.VideoWallFunc;
import com.mc.tool.framework.systemedit.datamodel.VisualEditFunc;
import com.mc.tool.framework.systemedit.datamodel.VisualEditFunc.PublishMode;
import com.mc.tool.framework.systemedit.datamodel.VisualEditModel;
import com.mc.tool.framework.systemedit.datamodel.VisualEditNode;
import com.mc.tool.framework.systemedit.datamodel.VisualEditTerminal;
import com.mc.tool.framework.systemedit.view.ViewUtility;
import com.mc.tool.framework.utility.FormDialog;
import com.mc.tool.framework.utility.I18nUtility;
import com.mc.tool.framework.utility.UndecoratedAlert;
import java.net.URL;
import java.text.MessageFormat;
import java.util.ArrayList;
import java.util.Collection;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.ResourceBundle;
import java.util.Set;
import java.util.function.Predicate;
import java.util.stream.Collectors;
import javafx.beans.binding.Bindings;
import javafx.beans.binding.StringBinding;
import javafx.beans.property.BooleanProperty;
import javafx.beans.property.ObjectProperty;
import javafx.beans.property.ReadOnlyStringWrapper;
import javafx.beans.property.SimpleBooleanProperty;
import javafx.beans.property.SimpleObjectProperty;
import javafx.beans.property.SimpleStringProperty;
import javafx.beans.property.StringProperty;
import javafx.beans.value.ChangeListener;
import javafx.beans.value.WeakChangeListener;
import javafx.collections.FXCollections;
import javafx.collections.ListChangeListener;
import javafx.collections.ObservableList;
import javafx.event.EventHandler;
import javafx.geometry.Point2D;
import javafx.geometry.Point3D;
import javafx.geometry.Pos;
import javafx.geometry.Rectangle2D;
import javafx.scene.control.AlertEx.AlertExType;
import javafx.scene.control.ContextMenu;
import javafx.scene.control.Menu;
import javafx.scene.control.MenuItem;
import javafx.scene.input.DragEvent;
import javafx.scene.input.MouseButton;
import javafx.scene.input.MouseEvent;
import javafx.scene.input.TransferMode;
import javafx.scene.paint.Color;
import javafx.util.Pair;
import lombok.extern.slf4j.Slf4j;
import org.controlsfx.control.BooleanPropertyItem2;
import org.controlsfx.control.NumberPropertyItem;
import org.controlsfx.control.ObjectPropertyItem;
import org.controlsfx.control.PropertySheet;
import org.controlsfx.control.StringPropertyItem;
import org.controlsfx.control.TextBlockPropertyItem;

/**
 * .
 */
@Slf4j
public class CaesarVideoWallController extends VideoWallOperationController {
  private CaesarDeviceController deviceController;
  private ChangeListener<VpGroup> screenListener;
  private ChangeListener<Number> screenBindingPortListener;
  private ChangeListener<Number> osdNumberChangeListener;
  private ChangeListener<Boolean> booleanChangeListener;
  private ChangeListener<Color> osdColorChangeListener;
  private ChangeListener<CaesarTestFrameMode> testFrameChangeListener;
  private ChangeListener<Number> txRxGroupChangeListener;
  private ChangeListener<Color> testFrameColorChangeListener;
  private ChangeListener<Number> testFrameNumberChangeListener;

  private List<PageText> tempPageTexts = new ArrayList<>();

  private BooleanProperty editableProperty;
  private ChangeListener<Number> funcIndexChangeListener;
  private boolean videoWallChanged = false;

  private ObjectProperty<CaesarVpMatrix> vpMatrix = new SimpleObjectProperty<>();

  private Map<Integer, Vp6WallCurrCfg> vp6WallCurrCfg = new HashMap<>();

  private Map<Integer, Vp7WallCurrCfg> vp7WallCurrCfg = new HashMap<>();

  @Override
  public void setDeviceController(DeviceControllable deviceController) {
    if (deviceController instanceof CaesarDeviceController) {
      this.deviceController = (CaesarDeviceController) deviceController;
      vpMatrix.set(new CaesarVpMatrix(this.deviceController));
      editableProperty.set(
          this.deviceController.getCaesarUserRight().isVideoWallEditable(getCasearVideoWallFunc()));
    }
  }

  @Override
  public void initialize(URL location, ResourceBundle resources) {
    super.initialize(location, resources);
    listModeBtn.setVisible(true);
  }

  @Override
  public void init(VisualEditModel model, VisualEditFunc currentFunction) {
    super.init(model, currentFunction);
    // 监听显示单元的改变
    screenListener =
        weakAdapter.wrap(
            (observable, oldVal, newVal) -> {
              updateScreenText();
              onVideoWallChange();
            });
    screenBindingPortListener =
        weakAdapter.wrap(
            (observable, oldVal, newVal) -> {
              updateScreenText();
              onVideoWallChange();
            });

    osdNumberChangeListener = weakAdapter.wrap((observable, oldVal, newVal) -> onVideoWallChange());
    osdColorChangeListener = weakAdapter.wrap((observable, oldVal, newVal) -> onVideoWallChange());
    booleanChangeListener = weakAdapter.wrap((observable, oldVal, newVal) -> onVideoWallChange());
    testFrameChangeListener = weakAdapter.wrap((observable, oldVal, newVal) -> onVideoWallChange());
    testFrameColorChangeListener =
        weakAdapter.wrap((observable, oldVal, newVal) -> onVideoWallChange());
    testFrameNumberChangeListener =
        weakAdapter.wrap((observable, oldVal, newVal) -> onVideoWallChange());
    txRxGroupChangeListener =
        weakAdapter.wrap((observable, oldVal, newVal) -> {
          if (!oldVal.equals(newVal) && newVal.intValue() == 0) {
            removeTxRxGroupConnect(newVal);
          }
          onVideoWallChange();
        });
    editableProperty = new SimpleBooleanProperty();
    funcIndexChangeListener =
        weakAdapter.wrap(
            (observable, oldVal, newVal) -> {
              if (deviceController == null) {
                return;
              }
              editableProperty.set(
                  deviceController
                      .getCaesarUserRight()
                      .isVideoWallEditable(getCasearVideoWallFunc()));
            });
    getCasearVideoWallFunc().getVideoWallIndexProperty().addListener(funcIndexChangeListener);

    getVideoWallFunction()
        .getVideoWallObject()
        .getScreens()
        .addListener(
            weakAdapter.wrap((ListChangeListener<ScreenObject>) change -> updateScreenText()));

    CaesarOsdData osdData =
        ((CaesarVideoWallData) getVideoWallFunction().getVideoWallObject()).getOsdData();
    osdData.getOsdLeftProperty().addListener(osdNumberChangeListener);
    osdData.getOsdTopProperty().addListener(osdNumberChangeListener);
    osdData.getOsdHeightProperty().addListener(osdNumberChangeListener);
    osdData.getOsdWidthProperty().addListener(osdNumberChangeListener);
    osdData.getOsdAlphaProperty().addListener(osdNumberChangeListener);
    osdData.getOsdColorProperty().addListener(osdColorChangeListener);
    osdData.getBgColorProperty().addListener(osdColorChangeListener);
    osdData.getShowLogoProperty().addListener(booleanChangeListener);
    osdData.getEnableBgImgProperty().addListener(booleanChangeListener);
    osdData.getBgImgWidthProperty().addListener(osdNumberChangeListener);
    osdData.getBgImgHeightProperty().addListener(osdNumberChangeListener);
    osdData.getDisableSyncDataProperty().addListener(booleanChangeListener);
    osdData.getEnableRedundantProperty().addListener(booleanChangeListener);

    CaesarOutputData outputData =
        ((CaesarVideoWallData) getVideoWallFunction().getVideoWallObject()).getOutputData();
    outputData.enable.addListener(booleanChangeListener);
    outputData.clock.addListener(osdNumberChangeListener);
    outputData.horzBackPorch.addListener(osdNumberChangeListener);
    outputData.horzFrontPorch.addListener(osdNumberChangeListener);
    outputData.horzPolarity.addListener(booleanChangeListener);
    outputData.horzSync.addListener(osdNumberChangeListener);

    outputData.vertBackPorch.addListener(osdNumberChangeListener);
    outputData.vertFrontPorch.addListener(osdNumberChangeListener);
    outputData.vertPolarity.addListener(booleanChangeListener);
    outputData.vertSync.addListener(osdNumberChangeListener);

    CaesarTestFrameData testFrameData =
        ((CaesarVideoWallData) getVideoWallFunction().getVideoWallObject()).getTestFrameData();
    testFrameData.modeProperty().addListener(testFrameChangeListener);
    testFrameData.colorProperty().addListener(testFrameColorChangeListener);
    testFrameData.speedProperty().addListener(testFrameNumberChangeListener);
    testFrameData.alphaProperty().addListener(testFrameNumberChangeListener);

    ObjectProperty<Integer> audioGroupIndex =
        ((CaesarVideoWallData) getVideoWallFunction().getVideoWallObject()).getAudioGroupIndex();
    audioGroupIndex.addListener(txRxGroupChangeListener);

    for (ScreenObject screenObject : getVideoWallFunction().getVideoWallObject().getScreens()) {
      if (!(screenObject instanceof CaesarScreenData)) {
        continue;
      }
      CaesarScreenData data = (CaesarScreenData) screenObject;
      data.getBindingVpGroup().addListener(screenListener);
      data.getBindingPortIndex().addListener(screenBindingPortListener);
    }

    getVideoWallFunction()
        .getVideoWallObject()
        .getScreens()
        .addListener(
            weakAdapter.wrap(
                (ListChangeListener<ScreenObject>)
                    change -> {
                      while (change.next()) {
                        for (ScreenObject screenObject : change.getAddedSubList()) {
                          if (!(screenObject instanceof CaesarScreenData)) {
                            continue;
                          }
                          CaesarScreenData data = (CaesarScreenData) screenObject;
                          data.getBindingVpGroup().addListener(screenListener);
                          data.getBindingPortIndex().addListener(screenBindingPortListener);
                        }

                        for (ScreenObject screenObject : change.getRemoved()) {
                          if (!(screenObject instanceof CaesarScreenData)) {
                            continue;
                          }
                          CaesarScreenData data = (CaesarScreenData) screenObject;
                          data.getBindingVpGroup().removeListener(screenListener);
                          data.getBindingPortIndex().removeListener(screenBindingPortListener);
                        }
                      }
                    }));

    getVideoWallFunction()
        .getPublishModeProperty()
        .addListener(
            weakAdapter.wrap(
                (obs, oldVal, newVal) -> {
                  // 当从手动转成自动，先发布一次
                  if (newVal == PublishMode.AUTO) {
                    publish();
                  }
                }));
    WeakChangeListener<Object> listener =
        weakAdapter.wrap((observable, oldVal, newVal) -> updateGraph());
    getCasearVideoWallFunc().getVideoWallObject().getUseLogicLayout().addListener(listener);
    getCasearVideoWallFunc()
        .getVideoWallObject()
        .getLogicLayoutData()
        .getRowsProperty()
        .addListener(listener);
    getCasearVideoWallFunc()
        .getVideoWallObject()
        .getLogicLayoutData()
        .getColumnsProperty()
        .addListener(listener);
  }

  @Override
  public DeviceControllable getDeviceController() {
    return deviceController;
  }

  @Override
  protected void initVideoWallChecker() {
    CaesarVideoWallCheckerFunctions.registerFunctions(vpMatrix, videoWallChecker);
  }

  @Override
  protected void updateGraph() {
    IVideoWallLayout layout;
    if (graph.getPageCanvas().isGraphVisible()) {
      layout = func.getVideoWallObject().getUserLayout();
    } else {
      layout = func.getVideoWallObject().getLayoutData();
    }
    graph.getPageCanvas().setAllPageAreas(layout.getScreenAreas());
    updateScreenText();
    updateErrorScreens();
  }

  @Override
  public void switchScreenMode() {
    graph.getPageCanvas().setGraphVisible(false);
    tempPageTexts.clear();
    tempPageTexts.addAll(pageTexts);

    updateScreenText();
    updateGraph();
  }

  @Override
  protected void createContextMenuItems(ContextMenu menu) {
    if (!editableProperty.get()) {
      return;
    }
    super.createContextMenuItems(menu);
    menu.getItems().add(new MenuToTop(this));
    menu.getItems().add(new MenuToBottom(this));
    menu.getItems().add(new MenuToUp(this));
    menu.getItems().add(new MenuToDown(this));
    ObjectProperty<Integer> audioGroupIndex =
        ((CaesarVideoWallData) func.getVideoWallObject()).getAudioGroupIndex();
    if (audioGroupIndex.get() > 0 && this.getSelectedVideos().size() == 1) {
      CaesarVideoData next = (CaesarVideoData) this.getSelectedVideos().iterator().next();
      Menu audioRxMenu =
          new Menu(I18nUtility.getI18nBundle("operation").getString("menu.audio_rx"));
      List<ConsoleData> conDataByTxRxGroupIndex =
          deviceController.getDataModel().getConfigDataManager()
              .getConsoleDataByTxRxGroupIndex(audioGroupIndex.get());
      List<ConsoleData> conData = new ArrayList<>();
      for (ConsoleData dataByTxRxGroupIndex : conDataByTxRxGroupIndex) {
        if (func.getVideoWallObject().getVideos().stream().anyMatch(video -> {
              if (((CaesarVideoData) video).getAudioRx().get() == dataByTxRxGroupIndex.getId()) {
                return next.getAudioRx().get() != dataByTxRxGroupIndex.getIdProperty().get();
              }
              return false;
            }
        )) {
          continue;
        }
        conData.add(dataByTxRxGroupIndex);
      }
      if (!conData.isEmpty()) {
        audioRxMenu.getItems().add(new MenuAudioRx(this, null));
        for (ConsoleData con : conData) {
          int sourceIndex = next.getSourceIndex().get();
          CpuData cpuData =
              deviceController.getDataModel().getConfigDataManager().getCpuData(sourceIndex);
          boolean canConnect =
              TerminalUtility.getConnectableType(deviceController.getDataModel(), con, cpuData)
                  .stream().anyMatch(type -> type.getType().equals(TerminalUtility.VIDEO));
          if (canConnect) {
            audioRxMenu.getItems().add(new MenuAudioRx(this, con));
          }
        }
        menu.getItems().add(audioRxMenu);
      }
    }
  }

  /**
   * 更新屏幕的文字.
   */
  protected void updateScreenText() {
    int columns = getVideoWallFunction().getVideoWallObject().getLayoutData().getColumns();
    int rows = getVideoWallFunction().getVideoWallObject().getLayoutData().getRows();
    ObservableList<? extends ScreenObject> screenObjects =
        getVideoWallFunction().getVideoWallObject().getScreens();
    List<PageText> texts = new ArrayList<>();
    // 屏幕名称显示在物理布局中
    List<Rectangle2D> allScreenAreas =
        Lists.newArrayList(
            getVideoWallFunction().getVideoWallObject().getLayoutData().getScreenAreas());
    for (int i = 0; i < columns; i++) {
      for (int j = 0; j < rows; j++) {
        int index = j * columns + i;
        if (index >= screenObjects.size()) {
          break;
        }
        ScreenObject object = screenObjects.get(index);
        if (!(object instanceof CaesarScreenData)) {
          continue;
        }
        CaesarScreenData caesarScreenData = (CaesarScreenData) object;
        StringProperty textProperty =
            caesarScreenData.getVpScreen() == null ? new ReadOnlyStringWrapper("Empty") :
                caesarScreenData.getVpScreen().nameProperty();
        PageText text =
            new PageText(allScreenAreas.get(index), textProperty, Color.BLACK, Pos.CENTER);
        texts.add(text);
      }
    }
    pageTexts.setAll(texts);
  }

  @Override
  protected void initializeGraph() {
    super.initializeGraph();

    graph
        .getPageCanvas()
        .getBgCanvas()
        .setOnDragOver(
            (event) -> {
              if (event.getDragboard().hasString() && editableProperty.get()) {
                event.acceptTransferModes(TransferMode.ANY);
              }

              event.consume();
            });
    // 右击显示菜单
    graph
        .getPageCanvas()
        .getBgCanvas()
        .addEventFilter(
            MouseEvent.MOUSE_CLICKED,
            (event) -> {
              if (!editableProperty.get()) {
                event.consume();
                return;
              }
              if (event.getButton() == MouseButton.SECONDARY && event.getClickCount() == 1) {
                menu.getItems().clear();

                Collection<Integer> indexes = graph.getPageCanvas()
                    .getIntersectedPageAreasIndex(new Point2D(event.getX(), event.getY()));
                MenuItem debind = new MenuItem();
                debind.setText("解除绑定");
                debind.setDisable(true);
                for (int index : indexes) {
                  if (index < 0 || index >= func.getVideoWallObject().getScreenCount()) {
                    break;
                  }
                  ScreenObject screenObject = func.getVideoWallObject().getScreens().get(index);
                  if (screenObject instanceof CaesarScreenData) {
                    CaesarScreenData screenData = (CaesarScreenData) screenObject;
                    if (screenData.getVpScreen() != null) {
                      debind.setOnAction(
                          (actonEvent) -> screenData.setBingdingScreen(new Pair<>(null, 0)));
                      debind.setDisable(false);
                    }
                    break;
                  }
                }
                menu.getItems().add(debind);
                menu.show(graph.getCanvas().getContainer(), event.getScreenX(), event.getScreenY());
              } else {
                menu.hide();
              }
            });
    // 绑定显示
    graph
        .getPageCanvas()
        .getBgCanvas()
        .setOnDragDropped(
            (event) -> {
              double xpos = event.getX();
              double ypos = event.getY();
              Collection<Integer> areaIndexes =
                  graph.getPageCanvas().getIntersectedPageAreasIndex(new Point2D(xpos, ypos));
              // 查找绑定的vpconsoledata
              VideoWallFunc func = getVideoWallFunction();
              Pair<VpGroup, Integer> binding = null;
              VpConsoleData data = null;
              int id = Integer.parseInt(event.getDragboard().getString());
              for (VisualEditNode node : func.getChildren()) {
                if (!(node instanceof VpGroup)) {
                  continue;
                }
                VpGroup group = (VpGroup) node;
                for (VpConsoleData vpConsoleData : group.getOutPortsList()) {
                  if (vpConsoleData.getId() == id) {
                    data = vpConsoleData;
                    binding =
                        new Pair<>(group, group.getVpConsoleData().getOutportIndex(vpConsoleData));
                  }
                }
              }
              // 绑定
              if (binding != null) {
                beginUpdate();
                try {
                  for (int index : areaIndexes) {
                    if (index < 0 || index >= func.getVideoWallObject().getScreenCount()) {
                      break;
                    }
                    ScreenObject screenObject = func.getVideoWallObject().getScreens().get(index);

                    for (ScreenObject item : func.getVideoWallObject().getScreens()) {
                      if (item != screenObject && item instanceof CaesarScreenData) {
                        CaesarScreenData screenData = (CaesarScreenData) item;
                        if (screenData.getVpScreen() == data) {
                          screenData.setBingdingScreen(new Pair<>(null, 0));
                        }
                      }
                    }

                    if (screenObject instanceof CaesarScreenData) {
                      CaesarScreenData screenData = (CaesarScreenData) screenObject;
                      screenData.setBingdingScreen(binding);
                      break;
                    }
                  }
                } finally {
                  endUpdate();
                }
              }

              event.setDropCompleted(true);
              event.consume();
            });

    // 双击大屏窗口放大
    graph
        .getCanvas()
        .getContainer()
        .addEventFilter(
            MouseEvent.MOUSE_CLICKED,
            weakAdapter.wrap(
                (EventHandler<MouseEvent>)
                    (event) -> {
                      if (event.getButton() == MouseButton.PRIMARY && event.getClickCount() == 2) {
                        Collection<VideoObject> videos = getSelectedVideos();
                        beginUpdate();
                        for (VideoObject video : videos) {
                          stretchToFit(
                              getVideoWallFunction().getVideoWallObject().getUserLayout(), video);
                        }
                        endUpdate();
                      }
                    }));
  }

  private void stretchToFit(IVideoWallLayout layoutData, VideoObject videoData) {
    Rectangle2D videoRect =
        new Rectangle2D(
            videoData.getXpos().get(),
            videoData.getYpos().get(),
            videoData.getWidth().get(),
            videoData.getHeight().get());
    double minX = videoRect.getMinX();
    double minY = videoRect.getMinY();
    double maxX = videoRect.getMaxX();
    double maxY = videoRect.getMaxY();
    for (int i = 0; i < layoutData.getRows(); i++) {
      for (int j = 0; j < layoutData.getColumns(); j++) {
        Rectangle2D screenRect =
            PageCanvasUtility.getGridRect(
                layoutData.getWidths(), layoutData.getHeights(), new Point2D(j, i));
        if (videoRect.intersects(screenRect)) {
          if (screenRect.getMinX() < minX || screenRect.getMinY() < minY) {
            minX = screenRect.getMinX();
            minY = screenRect.getMinY();
          }
          if (screenRect.getMaxX() > maxX || screenRect.getMaxY() > maxY) {
            maxX = screenRect.getMaxX();
            maxY = screenRect.getMaxY();
          }
        }
      }
    }
    videoData.getXpos().set((int) minX);
    videoData.getYpos().set((int) minY);
    videoData.getWidth().set((int) (maxX - minX));
    videoData.getHeight().set((int) (maxY - minY));
  }

  @Override
  protected void initVideoGraphBehavior() {
    super.initVideoGraphBehavior();
    graph.cellDeletable().bind(editableProperty);
    graph.cellMovable().bind(editableProperty);
    graph.cellOrderable().bind(editableProperty);
    graph.cellResizable().bind(editableProperty);
    graph.cellRotatable().bind(editableProperty);
  }

  @Override
  public void switchVideoMode() {
    graph.getPageCanvas().setGraphVisible(true);

    pageTexts.setAll(tempPageTexts);
    tempPageTexts.clear();
    updateGraph();
  }

  @Override
  protected void updateErrorScreens() {
    // 高亮有错的屏幕
    Collection<VideoWallError> errors = videoWallChecker.check(func.getVideoWallObject());
    Set<Integer> screenIndexes = new HashSet<>();
    Set<Rectangle2D> screenAreas = new HashSet<>();
    // 显示错误信息需要使用物理的布局，不能使用用户布局
    LayoutData originLayout = func.getVideoWallObject().getLayoutData();
    List<PageText> texts = new ArrayList<>();
    List<Rectangle2D> allScreenAreas = Lists.newArrayList(originLayout.getScreenAreas());
    for (VideoWallError screenData : errors) {
      int index =
          func.getVideoWallObject().getScreenIndex(screenData.getScreenData());
      if (index < 0 || index >= allScreenAreas.size()) {
        log.warn("Fail to find screen data!");
        continue;
      }
      if (screenIndexes.contains(index)) {
        continue;
      }
      Rectangle2D screenArea = allScreenAreas.get(index);
      if (screenArea == null) {
        log.warn("Fail to find area in video wall graph by index " + index);
        continue;
      }
      screenAreas.add(screenArea);
      PageText text =
          new PageText(screenArea, new ReadOnlyStringWrapper(screenData.getErrorReason()),
              Color.RED, Pos.CENTER);
      texts.add(text);
    }

    graph.getPageCanvas().setHighLightAreas(screenAreas);
    if (!graph.getPageCanvas().isGraphVisible()) {
      tempPageTexts.clear();
      tempPageTexts.addAll(texts);
    } else {
      pageTexts.setAll(texts);
    }
  }

  @Override
  protected boolean checkUpdate() {
    if (!super.checkUpdate()) {
      return false;
    }
    return deviceController == null || !deviceController.isUpdating();
  }

  @Override
  protected void onVideoWallChange() {
    super.onVideoWallChange();
    if (!checkUpdate() || !(func instanceof CaesarVideoWallFunc)) {
      return;
    }
    videoWallChanged = true;
    if (func.getPublishMode() == PublishMode.AUTO) {
      updateData2Dev();
    }
  }

  @Override
  protected void onAddVideo(DragEvent event) {
    PageCanvas canvas = graph.getPageCanvas();

    if (event.getPickResult().getIntersectedNode() != canvas.getContainer()) {
      return;
    }
    if (!event.getDragboard().hasString()) {
      log.warn("No string store in drag board!");
    }

    VisualEditTerminal terminal = getDraggedVideoSource(event);
    DraggedVideoSourceInfo info = getDraggedVideoSourceInfo(event);

    Point3D graphLocation = event.getPickResult().getIntersectedPoint();

    Collection<Rectangle2D> areas =
        canvas.getIntersectedPageOriginAreas(
            new Point2D(graphLocation.getX(), graphLocation.getY()));
    if (areas.isEmpty()) {
      log.warn("No grid intersected!");
      return;
    }
    Rectangle2D rect = areas.iterator().next();

    int width = (int) rect.getWidth();
    int height = (int) rect.getHeight();

    VideoObject video = func.createVideo();
    video.getSource().set(terminal);
    video.getXpos().set((int) rect.getMinX());
    video.getYpos().set((int) (rect.getMinY()));
    video.getWidth().set(width);
    video.getHeight().set(height);
    if (video instanceof CaesarVideoData) {
      CaesarVideoData caesarVideoData = (CaesarVideoData) video;
      caesarVideoData.getSourceIndex().set(info.getSourceIndex());
      caesarVideoData.getClipIndex().set(info.getClipIndex());
      caesarVideoData.getClipName()
          .set(CaesarDataUtility.getClipName(deviceController.getDataModel().getConfigDataManager(), info.getClipIndex()));
    }
    VideoWallObject videoWallData = func.getVideoWallObject();
    videoWallData.addVideo(video);
  }

  protected DraggedVideoSourceInfo getDraggedVideoSourceInfo(DragEvent event) {

    try {
      if (event.getDragboard().hasUrl()) {
        String str = event.getDragboard().getUrl();
        DraggedVideoSourceInfo info = new Gson().fromJson(str, DraggedVideoSourceInfo.class);
        return info;
      } else {
        return new DraggedVideoSourceInfo();
      }
    } catch (RuntimeException exception) {
      log.warn("Fail to parse dragged video source info!", exception);
      return new DraggedVideoSourceInfo();
    }
  }

  @Override
  protected void onSwitchVideo(DragEvent event) {
    CellSkin skin = NodeUtil.node2CellSkin(event.getPickResult().getIntersectedNode());
    if (skin == null) {
      return;
    }
    Object bindedObject = skin.getCell().getBindedObject();
    if (!(bindedObject instanceof CaesarVideoData)) {
      return;
    }
    CaesarVideoData data = (CaesarVideoData) bindedObject;
    DraggedVideoSourceInfo info = getDraggedVideoSourceInfo(event);
    beginUpdate();
    data.getSourceIndex().set(info.getSourceIndex());
    data.getClipIndex().set(info.getClipIndex());
    data.getClipName()
        .set(CaesarDataUtility.getClipName(deviceController.getDataModel().getConfigDataManager(), info.getClipIndex()));
    VisualEditTerminal terminal = getDraggedVideoSource(event);
    data.getSource().set(terminal);
    endUpdate();
  }

  protected void updateData2Dev() {
    if (!videoWallChanged) {
      return;
    }
    // 页面还没有初始化
    if (graphBox == null || graphBox.getScene() == null) {
      return;
    }
    if (!deviceController.checkCanExecute()) {
      ViewUtility.showAlert(
          graphBox.getScene().getWindow(),
          CaesarI18nCommonResource.getString("alert.device_busy"),
          AlertExType.WARNING);
      log.warn("Can not update videowall for too many tasks!");
      return;
    }
    videoWallChanged = false;
    CaesarVideoWallFunc caesarVideoWallFunc = (CaesarVideoWallFunc) func;
    // TODO(gao) 暂时拼缝补偿像素(left,top,right,bottom)为(0,0,1,1)
    func.getVideoWallObject().getLayoutData().getLeftCompensation().set(0);
    func.getVideoWallObject().getLayoutData().getTopCompensation().set(0);
    func.getVideoWallObject().getLayoutData().getRightCompensation().set(1);
    func.getVideoWallObject().getLayoutData().getBottomCompensation().set(1);

    VideoWallObject copyVideoWall = func.createVideoWallObject();
    final int index = caesarVideoWallFunc.getVideoWallIndex();
    func.getVideoWallObject().copyTo(copyVideoWall, true);

    deviceController.execute(
        () -> {
          CaesarVideoWallData videoWallObject = (CaesarVideoWallData) func.getVideoWallObject();
          // 重新建立所有TX和音频RX连接
          List<ConsoleData> conDataByTxRxGroupIndex =
              deviceController.getDataModel().getConfigDataManager()
                  .getConsoleDataByTxRxGroupIndex(videoWallObject.getAudioGroupIndex().get());
          List<ConsoleData> connectedConsoles = new ArrayList<>();
          for (VideoObject videoData : copyVideoWall.getVideos()) {
            if (videoData instanceof CaesarVideoData
                && videoData.getSource().get() != null
                && ((CaesarVideoData) videoData).getAudioRx() != null
                && ((CaesarVideoData) videoData).getAudioRx().get() != 0) {
              ConsoleData con = deviceController.getDataModel().getConfigDataManager()
                  .getConsoleData4Id(((CaesarVideoData) videoData).getAudioRx().get());
              connectedConsoles.add(con);
              ExtendedSwitchUtility.videoAccess(deviceController.getDataModel(), con,
                  new CpuDataStateWrapper(
                      ((CaesarCpuTerminal) videoData.getSource().get()).getCpuData(),
                      CpuDataStateWrapper.Access.VIDEO), true);
            }
          }
          for (ConsoleData dataByTxRxGroupIndex : conDataByTxRxGroupIndex) {
            if (dataByTxRxGroupIndex != null && connectedConsoles.stream()
                .noneMatch(item -> item.getId() == dataByTxRxGroupIndex.getId())) {
              ExtendedSwitchUtility.disconnect(deviceController.getDataModel(), dataByTxRxGroupIndex,
                  true);
            }
          }
          if (copyVideoWall instanceof CaesarVideoWallData) {
            sendVideoWallConfigNew((CaesarVideoWallData) copyVideoWall, index);
          } else {
            log.warn("Error video wall data type!");
          }
        });
  }

  protected synchronized void sendVideoWallConfigNew(
      CaesarVideoWallData videoWallObject, int index) {
    if (videoWallObject.isVp6()) {
      sendVp6VideoWallConfig(videoWallObject, index);
    } else if (videoWallObject.isVp7()) {
      sendVp7VideoWallConfig(videoWallObject, index);
    } else {
      log.warn("Unsupported video wall type!");
    }
  }

  protected synchronized void sendVp6VideoWallConfig(
      CaesarVideoWallData videoWallObject, int index) {
    if (deviceController == null) {
      return;
    }
    if (vpMatrix.get() == null) {
      vpMatrix.set(new CaesarVpMatrix(deviceController));
    }
    CaesarVpMatrix matrix = vpMatrix.get();
    final long totalStart = System.currentTimeMillis();
    // 获取分辨率
    final long cpuResStart = totalStart;
    for (VideoObject object : videoWallObject.getVideos()) {
      if (object.getSource().get() instanceof CaesarCpuTerminal) {
        CaesarCpuTerminal terminal = (CaesarCpuTerminal) object.getSource().get();
        if (terminal.getExtenderData() == null) {
          continue;
        }
        deviceController.getDataModel().getExtResolution(terminal.getExtenderData());
      }
    }
    final long cpuResEnd = System.currentTimeMillis();

    final long computeStart = cpuResEnd;
    // 计算参数

    // 获取配置生成器
    Vp6WallCurrCfg currCfg = vp6WallCurrCfg.computeIfAbsent(index, k -> new Vp6WallCurrCfg());
    Set<VpConsoleData> removedVpcon = currCfg.getUsedVpCons().stream().map(matrix::findVpCon)
        .filter(Objects::nonNull)
        .collect(Collectors.toSet());
    CaesarVpVideoWall vpVideoWall = new CaesarVpVideoWall(videoWallObject);
    Vp6Processor.processVideoWall(matrix, vpVideoWall, currCfg);

    // 获取连接信息
    Map<ConsoleData, CpuData> connectList = new HashMap<>(Vp6Processor.getConnections(matrix, currCfg));
    final long computeEnd = System.currentTimeMillis();
    // 开始配置
    CaesarSwitchDataModel configDataModel = deviceController.getDataModel();
    CaesarSwitchDataModel masterDataModel;
    try {
      masterDataModel = Utilities.getMasterModel(configDataModel);
    } catch (ConfigException | BusyException exception) {
      log.warn("Fail to get master data model!", exception);
      return;
    }
    try {
      configDataModel.beginUpdateVideoWall();
    } catch (Exception exception) {
      log.warn("Fail to begin udpate video wall!", exception);
      return;
    }
    try {
      final long connectStart = System.currentTimeMillis();
      // 连接
      ExtendedSwitchUtility.batchVideoAccessConnect(masterDataModel, connectList);
      final long connectEnd = System.currentTimeMillis();

      // 发送配置
      final long sendStart = connectEnd;

      Vp6OutputData vp6OutputData = videoWallObject.createVp6Output();
      List<VpConsoleData> currentUsedVpcons =
          currCfg.getUsedVpCons().stream().map(matrix::findVpCon).filter(Objects::nonNull)
              .collect(Collectors.toList());
      for (VpConsoleData vpConsoleData : currentUsedVpcons) {
        Vp6ConfigData configData = currCfg.getVpconConfigData(vpConsoleData.getId());
        if (configData == null) {
          configData = new Vp6ConfigData();
        }
        vpConsoleData.setConfigData(configData);
        configDataModel.sendVpconConfigData(vpConsoleData);
        configDataModel.sendVpconOutputData(vpConsoleData, vp6OutputData);
        removedVpcon.remove(vpConsoleData);
      }

      for (VpConsoleData vpConsoleData : removedVpcon) {
        vpConsoleData.getConfigData().reset();
        configDataModel.sendVpconConfigData(vpConsoleData);
      }
      final long sendEnd = System.currentTimeMillis();

      // 备份
      final long backUpStart = sendEnd;
      VideoWallGroupData.VideoWallData data = new VideoWallGroupData.VideoWallData();
      CaesarDataUtility.caesarVideoWallData2VpVideoWallData(videoWallObject, data);
      data.setName(func.getName());
      for (VisualEditNode node : func.getChildren()) {
        if (node instanceof VpGroup) {
          data.addVpgroups(((VpGroup) node).getId());
        }
      }

      configDataModel.getVpDataModel().sendVideoWallData(index, data);
      final long backUpEnd = System.currentTimeMillis();
      final long totalEnd = backUpEnd;

      log.info("Get cpu res time:{}ms", cpuResEnd - cpuResStart);
      log.info("Compute time:{}ms", computeEnd - computeStart);
      log.info("Connect time:{}ms", connectEnd - connectStart);
      log.info("Send time:{}ms", sendEnd - sendStart);
      log.info("Backup time:{}ms", backUpEnd - backUpStart);
      log.info("Total time:{}ms", totalEnd - totalStart);
    } catch (BusyException | DeviceConnectionException | RuntimeException exception) {
      log.warn("Fail to send video config!", exception);
    } finally {
      try {
        configDataModel.endUpdateVideoWall();
      } catch (Exception exception2) {
        log.warn("Fail to end update video wall!", exception2);
      }
    }
  }

  protected synchronized void sendVp7VideoWallConfig(
      CaesarVideoWallData videoWallObject, int index) {
    if (deviceController == null) {
      return;
    }
    if (vpMatrix.get() == null) {
      vpMatrix.set(new CaesarVpMatrix(deviceController));
    }
    CaesarVpMatrix matrix = vpMatrix.get();
    final long totalStart = System.currentTimeMillis();
    // 获取分辨率
    final long cpuResStart = totalStart;
    for (VideoObject object : videoWallObject.getVideos()) {
      if (object.getSource().get() instanceof CaesarCpuTerminal) {
        CaesarCpuTerminal terminal = (CaesarCpuTerminal) object.getSource().get();
        if (terminal.getExtenderData() == null) {
          continue;
        }
        deviceController.getDataModel().getExtResolution(terminal.getExtenderData());
      }
    }
    final long cpuResEnd = System.currentTimeMillis();

    final long computeStart = cpuResEnd;
    // 计算参数
    Vp7WallCurrCfg currCfg = vp7WallCurrCfg.computeIfAbsent(index, k -> new Vp7WallCurrCfg());
    Set<VpConsoleData> removedVpcon = currCfg.getUsedVpCons().stream().map(matrix::findVpCon)
        .filter(Objects::nonNull).collect(Collectors.toSet());
    CaesarVpVideoWall vpVideoWall = new CaesarVpVideoWall(videoWallObject);
    Vp7Processor.Status status = Vp7Processor.processVideoWall(matrix, vpVideoWall, currCfg);
    log.info("Vp7Processor status:{}", status);
    // 发送配置
    final long computeEnd = System.currentTimeMillis();
    CaesarSwitchDataModel configDataModel = deviceController.getDataModel();
    try {
      configDataModel.beginUpdateVideoWall();
    } catch (Exception exception) {
      log.warn("Fail to begin udpate video wall!", exception);
      return;
    }
    try {
      final long sendStart = System.currentTimeMillis();
      List<VpConsoleData> currentUsedVpcons =
          currCfg.getUsedVpCons().stream().map(matrix::findVpCon).filter(Objects::nonNull)
              .collect(Collectors.toList());
      for (VpConsoleData vpConsoleData : currentUsedVpcons) {
        // 连接
        List<SwitchMultiviewAllVideoChannel> channels = Vp7Processor.getConnections(matrix, vpConsoleData, currCfg);
        log.info("vpcon {} connection:[{},{},{},{},{},{},{},{}]", vpConsoleData.getName(),
            !channels.isEmpty() && channels.get(0).getCpuData() != null ? channels.get(0).getCpuData().getId() : 0,
            channels.size() > 1 && channels.get(1).getCpuData() != null ? channels.get(1).getCpuData().getId() : 0,
            channels.size() > 2 && channels.get(2).getCpuData() != null ? channels.get(2).getCpuData().getId() : 0,
            channels.size() > 3 && channels.get(3).getCpuData() != null ? channels.get(3).getCpuData().getId() : 0,
            channels.size() > 4 && channels.get(4).getCpuData() != null ? channels.get(4).getCpuData().getId() : 0,
            channels.size() > 5 && channels.get(5).getCpuData() != null ? channels.get(5).getCpuData().getId() : 0,
            channels.size() > 6 && channels.get(6).getCpuData() != null ? channels.get(6).getCpuData().getId() : 0,
            channels.size() > 7 && channels.get(7).getCpuData() != null ? channels.get(7).getCpuData().getId() : 0);

        deviceController.getDataModel().switchMultiviewAllVideo(vpConsoleData.getInPort(0), channels);
        // 配置
        Vp7ConfigData configData = currCfg.getVpconConfigData(vpConsoleData.getId());
        if (configData == null) {
          configData = new Vp7ConfigData();
        }
        vpConsoleData.setConfigData(configData);
        configDataModel.sendVpconConfigData(vpConsoleData);
        removedVpcon.remove(vpConsoleData);
      }

      for (VpConsoleData vpConsoleData : removedVpcon) {
        vpConsoleData.getConfigData().reset();
        configDataModel.sendVpconConfigData(vpConsoleData);
      }
      final long sendEnd = System.currentTimeMillis();

      // 备份
      final long backUpStart = sendEnd;
      VideoWallGroupData.VideoWallData data = new VideoWallGroupData.VideoWallData();
      CaesarDataUtility.caesarVideoWallData2VpVideoWallData(videoWallObject, data);
      data.setName(func.getName());
      for (VisualEditNode node : func.getChildren()) {
        if (node instanceof VpGroup) {
          data.addVpgroups(((VpGroup) node).getId());
        }
      }

      configDataModel.getVpDataModel().sendVideoWallData(index, data);
      final long backUpEnd = System.currentTimeMillis();
      final long totalEnd = backUpEnd;

      log.info("Get cpu res time:{}ms", cpuResEnd - cpuResStart);
      log.info("Compute time:{}ms", computeEnd - computeStart);
      log.info("Send time:{}ms", sendEnd - sendStart);
      log.info("Backup time:{}ms", backUpEnd - backUpStart);
      log.info("Total time:{}ms", totalEnd - totalStart);
    } catch (BusyException | DeviceConnectionException | RuntimeException | ConfigException exception) {
      log.warn("Fail to send video config!", exception);
    } finally {
      try {
        configDataModel.endUpdateVideoWall();
      } catch (Exception exception2) {
        log.warn("Fail to end update video wall!", exception2);
      }
    }

  }

  @Override
  public void onConfig() {
    CaesarConfigBean bean = new CaesarConfigBean((CaesarVideoWallData) func.getVideoWallObject());
    FormDialog<CaesarConfigBean> formDialog =
        new FormDialog<>(bean, CommonResource.getResourceBundle(), graphBox.getScene().getWindow());
    formDialog.getDialogPane().setMinSize(400, 400);
    formDialog.showAndWaitWithCommit();
  }

  @Override
  public ObservableList<PropertySheet.Item> getWindowsProperties() {
    ObservableList<PropertySheet.Item> layoutProperties = FXCollections.observableArrayList();
    return layoutProperties;
  }

  @Override
  public ObservableList<PropertySheet.Item> getLayoutPropperties() {
    ObservableList<PropertySheet.Item> videoProperties = FXCollections.observableArrayList();
    final CaesarVideoWallData videoWallData = (CaesarVideoWallData) func.getVideoWallObject();

    NumberPropertyItem index =
        new NumberPropertyItem(getCasearVideoWallFunc().getVideoWallIndexProperty(), Integer.class);
    index.setEditable(false);
    index.setName(Bundle.NbBundle.getMessage("Index"));
    videoProperties.add(index);

    NumberPropertyItem rows =
        new NumberPropertyItem(videoWallData.getLayoutData().getRowsProperty(), Integer.class);
    rows.setValueRange(1, CaesarConstants.MAX_VIDEOWALL_ROW);
    rows.setName(Bundle.NbBundle.getMessage("Layout.rows"));
    videoProperties.add(rows);
    NumberPropertyItem columns =
        new NumberPropertyItem(videoWallData.getLayoutData().getColumnsProperty(), Integer.class);
    columns.setValueRange(1, CaesarConstants.MAX_VIDEOWALL_COL);
    columns.setName(Bundle.NbBundle.getMessage("Layout.columns"));
    videoProperties.add(columns);
    // 多分辨率
    BooleanPropertyItem2 multiResItem =
        new BooleanPropertyItem2(videoWallData.getLayoutData().getMultiRes());
    multiResItem.setName(Bundle.NbBundle.getMessage("Layout.enable_multi_res"));
    videoProperties.add(multiResItem);

    TextBlockPropertyItem item = new TextBlockPropertyItem(new SimpleObjectProperty<>("..."));
    item.setName(Bundle.NbBundle.getMessage("Layout.multi_res"));
    item.setTextAction(
        (event) -> {
          if (!videoWallData.getLayoutData().getMultiRes().get()) {
            return;
          }
          MultiResolutionConfigView.config(
              graphBox.getScene().getWindow(),
              videoWallData.getLayoutData(),
              Bundle.NbBundle.getMessage("Layout.multi_res_config"));
        });
    videoProperties.add(item);

    NumberPropertyItem resWidth =
        new NumberPropertyItem(videoWallData.getLayoutData().getResWidth(), Integer.class);
    resWidth.setName(Bundle.NbBundle.getMessage("Layout.resWidth"));
    videoProperties.add(resWidth);

    NumberPropertyItem resHeight =
        new NumberPropertyItem(videoWallData.getLayoutData().getResHeight(), Integer.class);
    resHeight.setName(Bundle.NbBundle.getMessage("Layout.resHeight"));
    videoProperties.add(resHeight);

    // osd
    NumberPropertyItem osdLeft =
        new NumberPropertyItem(videoWallData.getOsdData().getOsdLeftProperty(), Integer.class);
    osdLeft.setName(Bundle.NbBundle.getMessage("Video.left"));
    videoProperties.add(osdLeft);
    NumberPropertyItem osdTop =
        new NumberPropertyItem(videoWallData.getOsdData().getOsdTopProperty(), Integer.class);
    osdTop.setName(Bundle.NbBundle.getMessage("Video.top"));
    videoProperties.add(osdTop);
    NumberPropertyItem osdWidth =
        new NumberPropertyItem(videoWallData.getOsdData().getOsdWidthProperty(), Integer.class);
    osdWidth.setName(Bundle.NbBundle.getMessage("Video.width"));
    videoProperties.add(osdWidth);
    NumberPropertyItem osdHeight =
        new NumberPropertyItem(videoWallData.getOsdData().getOsdHeightProperty(), Integer.class);
    osdHeight.setName(Bundle.NbBundle.getMessage("Video.height"));
    videoProperties.add(osdHeight);
    NumberPropertyItem osdAlpha =
        new NumberPropertyItem(videoWallData.getOsdData().getOsdAlphaProperty(), Double.class);
    osdAlpha.setId("osd-alpha-property");
    osdAlpha.setValueRange(0.0, 1.0);
    osdAlpha.setPrecision(2);
    osdAlpha.setName(Bundle.NbBundle.getMessage("Video.alpha"));
    videoProperties.add(osdAlpha);
    ObjectPropertyItem<Color> osdColor =
        new ObjectPropertyItem<>(videoWallData.getOsdData().getOsdColorProperty(), Color.class);
    osdColor.setName(Bundle.NbBundle.getMessage("Video.osdColor"));
    videoProperties.add(osdColor);
    ObjectPropertyItem<Color> bgColor =
        new ObjectPropertyItem<>(videoWallData.getOsdData().getBgColorProperty(), Color.class);
    bgColor.setName(Bundle.NbBundle.getMessage("Video.bgColor"));
    videoProperties.add(bgColor);
    
    // 关闭数据同步
    BooleanPropertyItem2 disableSyncData = new BooleanPropertyItem2(
        videoWallData.getOsdData().getDisableSyncDataProperty());
    disableSyncData.setName(Bundle.NbBundle.getMessage("Video.disableSyncData"));
    videoProperties.add(disableSyncData);
    // 冗余
    BooleanPropertyItem2 enableRedundancy = new BooleanPropertyItem2(
        videoWallData.getOsdData().getEnableRedundantProperty());
    enableRedundancy.setName(Bundle.NbBundle.getMessage("Video.enableRedundancy"));
    videoProperties.add(enableRedundancy);
    // 显示背景图
    BooleanPropertyItem2 enableBgImg =
        new BooleanPropertyItem2(videoWallData.getOsdData().getEnableBgImgProperty());
    enableBgImg.setName(Bundle.NbBundle.getMessage("Video.enableBgImg"));
    videoProperties.add(enableBgImg);
    if (videoWallData.isVp6()) {
      // 背景图上传.
      TextBlockPropertyItem bgImagePath =
          new TextBlockPropertyItem(new SimpleObjectProperty<>("..."));
      bgImagePath.setId("bgimg-upload-property");
      VisualEditNode parent = func.getParent();
      for (int loop = 0; loop < 100; loop++) {
        if (parent instanceof CaesarMatrix) {
          break;
        }
        if (parent.getParent() == null) {
          break;
        }
        parent = parent.getParent();
      }
      CaesarSwitchDataModel model;
      if (parent instanceof CaesarMatrix) {
        CaesarMatrix matrix = (CaesarMatrix) parent;
        try {
          model = Utilities.getExternalModel(deviceController.getDataModel(), matrix.getIp());
        } catch (BusyException | ConfigException ex) {
          model = deviceController.getDataModel();
          log.warn("Can not find uploadImg DataModel.", ex);
        }
      } else {
        model = deviceController.getDataModel();
        log.warn("Vp6 Can not find CaesarMatrix parent.");
      }
      CaesarSwitchDataModel finalModel = model;
      bgImagePath.setTextAction(
          (event) ->
              UploadBgImgView.show(
                  graphBox.getScene().getWindow(),
                  finalModel,
                  getCasearVideoWallFunc(),
                  CaesarI18nCommonResource.getString("videowall.bgimg.title")));
      bgImagePath.setName(CaesarI18nCommonResource.getString("videowall.bgimg.title"));
      videoProperties.add(bgImagePath);
      // 背景图宽度
      NumberPropertyItem bgImageWidth =
          new NumberPropertyItem(videoWallData.getOsdData().getBgImgWidthProperty(), Integer.class);
      bgImageWidth.setName(Bundle.NbBundle.getMessage("Video.bgimgwidth"));
      bgImageWidth.setId("bgimg-width-property");
      videoProperties.add(bgImageWidth);
      // 背景图高度
      NumberPropertyItem bgImageHeight =
          new NumberPropertyItem(videoWallData.getOsdData().getBgImgHeightProperty(), Integer.class);
      bgImageHeight.setName(Bundle.NbBundle.getMessage("Video.bgimgheight"));
      bgImageHeight.setId("bgimg-height-property");
      videoProperties.add(bgImageHeight);

      // 补偿放大阈值
      NumberPropertyItem compensationThreshold =
          new NumberPropertyItem(
              videoWallData.getLayoutData().getCompensationScaleThreshold(), Integer.class);
      compensationThreshold.setName(Bundle.NbBundle.getMessage("Layout.compensationScaleThreshold"));
      videoProperties.add(compensationThreshold);
      // 左侧拼缝补偿
      NumberPropertyItem leftCompensation =
          new NumberPropertyItem(
              videoWallData.getLayoutData().getLeftCompensation(), Integer.class);
      leftCompensation.setName(Bundle.NbBundle.getMessage("Layout.leftCompensation"));
      leftCompensation.setEditable(false);
      videoProperties.add(leftCompensation);
      // 右侧拼缝补偿
      NumberPropertyItem rightCompensation =
          new NumberPropertyItem(
              videoWallData.getLayoutData().getRightCompensation(), Integer.class);
      rightCompensation.setName(Bundle.NbBundle.getMessage("Layout.rightCompensation"));
      rightCompensation.setEditable(false);
      videoProperties.add(rightCompensation);
      // 上侧拼缝补偿
      NumberPropertyItem topCompensation =
          new NumberPropertyItem(videoWallData.getLayoutData().getTopCompensation(), Integer.class);
      topCompensation.setName(Bundle.NbBundle.getMessage("Layout.topCompensation"));
      topCompensation.setEditable(false);
      videoProperties.add(topCompensation);
      // 下侧拼缝补偿
      NumberPropertyItem bottomCompensation =
          new NumberPropertyItem(
              videoWallData.getLayoutData().getBottomCompensation(), Integer.class);
      bottomCompensation.setName(Bundle.NbBundle.getMessage("Layout.bottomCompensation"));
      bottomCompensation.setEditable(false);
      videoProperties.add(bottomCompensation);
    }
    return videoProperties;
  }

  @Override
  public ObservableList<PropertySheet.Item> getVideoLayoutPropperties() {
    ObservableList<PropertySheet.Item> videoLayoutProperties = FXCollections.observableArrayList();
    graph
        .getSelectionModel()
        .getSelectedItems()
        .addListener(
            weakAdapter.wrap(
                (ListChangeListener<SelectableNode>)
                    change -> {
                      while (change.next()) {
                        Collection<VideoObject> videoCollection = getSelectedVideos();
                        videoLayoutProperties.clear();
                        if (!videoCollection.isEmpty()) {
                          VideoObject video = videoCollection.iterator().next();
                          CaesarVideoData caesarVideoData = (CaesarVideoData) video;

                          StringProperty nameProperty = new SimpleStringProperty();
                          StringBinding nameBinding =
                              Bindings.createStringBinding(
                                  () -> {
                                    if (caesarVideoData.getSource().get() == null) {
                                      return "";
                                    } else {
                                      return caesarVideoData.getSource().get().getName()
                                          + (caesarVideoData.getSource().get().canSeperate()
                                          ? "("
                                          + (caesarVideoData.getSourceIndex().get() + 1)
                                          + ")"
                                          : "");
                                    }
                                  },
                                  caesarVideoData.getSource(),
                                  caesarVideoData.getSourceIndex());
                          nameProperty.bind(nameBinding);

                          StringPropertyItem cpu = new StringPropertyItem(nameProperty);
                          cpu.setName(Bundle.NbBundle.getMessage("videoLayout.cpu"));
                          cpu.setEditable(false);
                          videoLayoutProperties.add(cpu);
                          StringPropertyItem name = new StringPropertyItem(video.getName());
                          name.setName(Bundle.NbBundle.getMessage("videoLayout.name"));
                          videoLayoutProperties.add(name);
                          NumberPropertyItem videoXpos =
                              new NumberPropertyItem(video.getXpos(), Integer.class);
                          videoXpos.setName(Bundle.NbBundle.getMessage("videoLayout.xpos"));
                          videoLayoutProperties.add(videoXpos);
                          NumberPropertyItem videoYpos =
                              new NumberPropertyItem(video.getYpos(), Integer.class);
                          videoYpos.setName(Bundle.NbBundle.getMessage("videoLayout.ypos"));
                          videoLayoutProperties.add(videoYpos);
                          NumberPropertyItem videoWidth =
                              new NumberPropertyItem(video.getWidth(), Integer.class);
                          videoWidth.setName(Bundle.NbBundle.getMessage("videoLayout.width"));
                          videoLayoutProperties.add(videoWidth);
                          NumberPropertyItem videoHeight =
                              new NumberPropertyItem(video.getHeight(), Integer.class);
                          videoHeight.setName(Bundle.NbBundle.getMessage("videoLayout.height"));
                          videoLayoutProperties.add(videoHeight);
                          // VP7没有透明度
                          if (!getCasearVideoWallFunc().isVp7()) {
                            NumberPropertyItem transparence =
                                new NumberPropertyItem(video.getAlpha(), Double.class);
                            transparence.setValueRange(0, 1);
                            transparence.setName(
                                Bundle.NbBundle.getMessage("videoLayout.transparence"));
                            videoLayoutProperties.add(transparence);
                          }
                        }
                      }
                    }));
    return videoLayoutProperties;
  }

  @Override
  public void publish() {
    updateData2Dev();
  }

  @Override
  public void activeScenario(VideoWallObject scenario) {
    if (!deviceController.checkCanExecute()) {
      ViewUtility.showAlert(
          graphBox.getScene().getWindow(),
          CaesarI18nCommonResource.getString("alert.device_busy"),
          AlertExType.WARNING);
      log.warn("Can not activate scenario for too many tasks!");
      return;
    }
    if (scenario == null) {
      return;
    }
    beginUpdate();
    try {
      CaesarVideoWallData currentVideoWall =
          (CaesarVideoWallData) getVideoWallFunction().getVideoWallObject();
      if (currentVideoWall != null) {
        final String name = currentVideoWall.getName().get();
        final int bgImgWidth = currentVideoWall.getOsdData().getBgImgWidth();
        final int bgImgHeight = currentVideoWall.getOsdData().getBgImgHeight();
        final boolean disableSyncData = currentVideoWall.getOsdData().isDisableSyncData();
        final boolean enableRedundant = currentVideoWall.getOsdData().isEnableRedundant();
        scenario.copyTo(currentVideoWall, false);
        // 名称与背景图参数不能变
        currentVideoWall.getName().set(name);
        currentVideoWall.getOsdData().setBgImgWidth(bgImgWidth);
        currentVideoWall.getOsdData().setBgImgHeight(bgImgHeight);
        currentVideoWall.getOsdData().setDisableSyncData(disableSyncData);
        currentVideoWall.getOsdData().setEnableRedundant(enableRedundant);
        fixVideos(getVideoWallFunction().getVideoWallObject());
        currentVideoWall.getLayoutData().getMultiResObservable().update();
      }
    } finally {
      endUpdate();
    }
    if (func.getScenarios().contains(scenario)) {
      currentScenario.set(scenario);
    }
  }

  @Override
  public void saveScenario() {
    if (!canSaveScenario()) {
      ViewUtility.showAlert(
          graphBox.getScene().getWindow(),
          CaesarI18nCommonResource.getString("alert.can_not_save_scenario"),
          AlertExType.ERROR);
      log.error("Can not save scenario but somewhere execute it!", new Exception());
      return;
    }
    if (!deviceController.checkCanExecute()) {
      ViewUtility.showAlert(
          graphBox.getScene().getWindow(),
          CaesarI18nCommonResource.getString("alert.device_busy"),
          AlertExType.WARNING);
      log.warn("Can not save scenario for too many tasks!");
      return;
    }

    if (currentScenario.get() != null && func.getScenarios().contains(currentScenario.get())) {
      // 保留当前的名字
      String name = currentScenario.get().getName().get();
      Optional<String> result =
          ViewUtility.getNameFromDialog(
              graphBox.getScene().getWindow(), name, null, new CaesarNamePredicate());
      if (result.isPresent()) {
        name = result.get();
        func.getVideoWallObject().copyTo(currentScenario.get(), false);
        currentScenario.get().getName().set(name);
        if (currentScenario.get() instanceof CaesarVideoWallData) {
          CaesarVideoWallData videoWallData = (CaesarVideoWallData) currentScenario.get();
          ScenarioData scenarioData =
              deviceController
                  .getDataModel()
                  .getVpDataModel()
                  .getScenarioData(videoWallData.getId());
          if (scenarioData == null) {
            log.warn("Fail to find scenario {}!", videoWallData.getId());
            scenarioData =
                deviceController
                    .getDataModel()
                    .getVpDataModel()
                    .getFreeScenarioData(getCasearVideoWallFunc().getVideoWallIndex());
            if (scenarioData != null) {
              videoWallData.setId(scenarioData.getIndex());
              scenarioData.setConfigName(videoWallData.getName().get());
            }
          }
          if (scenarioData != null) {
            scenarioData.setConfigName(name);
            sendScenarioData(scenarioData);
          } else {
            log.warn("Fail to save scenario {}!", videoWallData.getName().get());
          }
        }
        scenarioList.refresh();
        simpleScenarioList.refresh();
      }
    } else {
      saveAsScenario();
    }
  }

  @Override
  public void saveAsScenario() {
    if (!canSaveScenario()) {
      log.error("Can not save scenario but somewhere execute it!", new Exception());
      return;
    }
    if (func.getScenarios().size() >= VideoWallGroupData.VIDEOWALL_SCENARIO_COUNT) {
      UndecoratedAlert alert = new UndecoratedAlert(AlertExType.WARNING);
      if (graphBox != null && graphBox.getScene() != null) {
        alert.initOwner(graphBox.getScene().getWindow());
      }
      alert.setContentText(
          MessageFormat.format(
              CaesarI18nCommonResource.getString("videowall.scenario.limit_error"),
              VideoWallGroupData.VIDEOWALL_SCENARIO_COUNT));
      alert.showAndWait();
      return;
    }
    if (!deviceController.checkCanExecute()) {
      ViewUtility.showAlert(
          graphBox.getScene().getWindow(),
          CaesarI18nCommonResource.getString("alert.device_busy"),
          AlertExType.WARNING);
      log.warn("Can not save scenario for too many tasks!");
      return;
    }
    ScenarioData scenarioData =
        deviceController
            .getDataModel()
            .getVpDataModel()
            .getFreeScenarioData(getCasearVideoWallFunc().getVideoWallIndex());
    if (scenarioData == null) {
      log.warn("Fail to save as scenario!");
      return;
    }

    Predicate<String> predicate = new CaesarNamePredicate();
    VideoWallFunc func = getVideoWallFunction();
    Optional<String> result =
        ViewUtility.getNameFromDialog(graphBox.getScene().getWindow(), "", null, predicate);
    if (result.isPresent()) {
      String name = result.get();
      CaesarVideoWallData videoWallData = (CaesarVideoWallData) func.createVideoWallObject();
      func.getVideoWallObject().copyTo(videoWallData, false);
      videoWallData.getName().set(name);
      func.addScenario(videoWallData);
      currentScenario.set(videoWallData);

      videoWallData.setId(scenarioData.getIndex());
      scenarioData.setConfigName(name);
      sendScenarioData(scenarioData);
    } else {
      deviceController.getDataModel().getVpDataModel().removeFreeScenarioData(scenarioData);
    }
  }

  protected void sendScenarioData(ScenarioData scenarioData) {
    VideoWallFunc func = getVideoWallFunction();
    // 向设备发送预案数据
    CaesarDataUtility.caesarVideoWallData2VpVideoWallData(
        (CaesarVideoWallData) func.getVideoWallObject(), scenarioData.getVideoWallData());

    // 获取连接信息与配置信息
    Map<ConsoleData, CpuData> connectList = new HashMap<>();
    Map<ConsoleData, Pair<VpConConfigData, Vp6OutputData>> scenarioConfigs = new HashMap<>();
    scenarioData.setConnections(connectList);
    scenarioData.setConfigs(scenarioConfigs);

    deviceController.execute(
        () -> deviceController.getDataModel().getVpDataModel().sendScenarioData(scenarioData));
  }

  @Override
  public void deleteScenario(VideoWallObject scenario) {
    super.deleteScenario(scenario);
    if (scenario instanceof CaesarVideoWallData) {
      CaesarVideoWallData caesarScenario = (CaesarVideoWallData) scenario;
      deviceController.execute(
          () ->
              deviceController
                  .getDataModel()
                  .getVpDataModel()
                  .removeScenarioData(caesarScenario.getId()));
    }
  }

  @Override
  protected void updateVideoDataListener(VideoObject data) {
    super.updateVideoDataListener(data);
    if (data instanceof CaesarVideoData) {
      ((CaesarVideoData) data).getSourceIndex().addListener(videoChangeListener);
      ((CaesarVideoData) data).getClipIndex().addListener(videoChangeListener);
      ((CaesarVideoData) data).getAudioRx().addListener(videoChangeListener);
    }
  }

  @Override
  protected void removeVideoDataListener(VideoObject data) {
    super.removeVideoDataListener(data);
    if (data instanceof CaesarVideoData) {
      ((CaesarVideoData) data).getSourceIndex().removeListener(videoChangeListener);
      ((CaesarVideoData) data).getClipIndex().removeListener(videoChangeListener);
      ((CaesarVideoData) data).getAudioRx().removeListener(videoChangeListener);
    }
  }

  protected CaesarVideoWallFunc getCasearVideoWallFunc() {
    if (func instanceof CaesarVideoWallFunc) {
      return (CaesarVideoWallFunc) func;
    } else {
      return null;
    }
  }

  @Override
  public void refreshOnShow() {
    updateErrorScreens();
  }

  /**
   * 移除音频镜像组的连接.
   */
  private void removeTxRxGroupConnect(Number txRxGroupId) {
    List<ConsoleData> cons =
        deviceController.getDataModel().getConfigDataManager()
            .getConsoleDataByTxRxGroupIndex(txRxGroupId.intValue());
    deviceController.execute(() -> {
      for (ConsoleData con : cons) {
        ExtendedSwitchUtility.disconnect(
            deviceController.getDataModel(), con, true);
      }
    });
  }
}
