package com.mc.tool.caesar.vpm.pages.systemedit.topological.view;

import com.mc.graph.interfaces.CellBindedObject;
import com.mc.graph.interfaces.CellObject;
import com.mc.graph.interfaces.ConnectorSkin;
import com.mc.graph.interfaces.SkinManager;
import com.mc.tool.caesar.vpm.pages.systemedit.topological.TopologicalUtility;
import com.mc.tool.caesar.vpm.pages.systemedit.topological.data.GridMatrixItem;
import com.mc.tool.framework.systemedit.datamodel.SystemEditDefinition;
import com.mc.tool.framework.systemedit.view.AbstractCellSkinEx;
import com.mc.tool.framework.systemedit.view.SystemeditConstants;
import edu.umd.cs.findbugs.annotations.SuppressFBWarnings;
import java.util.Collection;
import java.util.HashMap;
import java.util.Map;
import javafx.geometry.Pos;
import javafx.scene.Node;
import javafx.scene.Parent;
import javafx.scene.control.Label;
import javafx.scene.control.Tooltip;
import javafx.scene.layout.HBox;
import javafx.scene.layout.Pane;
import javafx.scene.layout.Priority;
import javafx.scene.layout.Region;
import javafx.scene.paint.Color;
import javafx.scene.shape.Circle;
import javafx.scene.text.TextAlignment;
import javafx.scene.transform.Rotate;

/**
 * .
 */
@SuppressFBWarnings("UWF_FIELD_NOT_INITIALIZED_IN_CONSTRUCTOR")
public class CaesarGridMatrixCellSkin extends AbstractCellSkinEx {

  private static final int LABEL_PADDING = 5;
  private Label nameLabel;
  private Circle circle;
  private HBox region;

  private Map<Object, ConnectorSkin> connectorSkins;

  /**
   * Contructor.
   *
   * @param cellobject cell object
   * @param parent parent
   * @param container container
   * @param skinManager skin manager
   */
  public CaesarGridMatrixCellSkin(
      CellObject cellobject, Parent parent, Parent container, SkinManager skinManager) {
    super(cellobject, parent, container, skinManager);
  }
  /// 继承自AbstractCellSkinEx

  @Override
  protected Label getNameLabel() {
    return nameLabel;
  }

  @Override
  protected Node[] getNameLabelChangeSource() {
    return new Node[0];
  }

  @Override
  protected void initInner() {
    connectorSkins = new HashMap<>();
  }

  @Override
  protected void initRegion() {
    region = new HBox();
    region.setUserData(this);
    region.setMinWidth(TopologicalUtility.REGION_WIDTH);
    region.setMaxWidth(TopologicalUtility.REGION_WIDTH);
    region.setMinHeight(TopologicalUtility.REGION_HEIGHT);
    region.setMaxHeight(TopologicalUtility.REGION_HEIGHT);
    region.setAlignment(Pos.CENTER);

    nameLabel = new Label();

    region.getChildren().add(nameLabel);
    Pane pane = new Pane();
    region.getChildren().add(pane);
    HBox.setHgrow(pane, Priority.ALWAYS);

    circle = new Circle();
    circle.setRadius(TopologicalUtility.CIRCLE_RADIUS);
    circle.setStroke(Color.web("#969899"));
    circle.setFill(Color.web("#e9eef2"));
    region.getChildren().add(circle);

    circle.setCenterX(TopologicalUtility.REGION_WIDTH - TopologicalUtility.CIRCLE_RADIUS);
    circle.setCenterY(TopologicalUtility.REGION_HEIGHT / 2);
    nameLabel.setLayoutX(0);
    nameLabel.setLayoutY(TopologicalUtility.REGION_HEIGHT / 2 - TopologicalUtility.CIRCLE_RADIUS);
    nameLabel.setMinHeight(TopologicalUtility.CIRCLE_RADIUS * 2);
    nameLabel.setMaxHeight(nameLabel.getMinHeight());
    nameLabel.setAlignment(Pos.CENTER_RIGHT);
    nameLabel.setTextAlignment(TextAlignment.RIGHT);
    nameLabel.setMinWidth(
        TopologicalUtility.REGION_WIDTH - TopologicalUtility.CIRCLE_RADIUS * 2 - LABEL_PADDING);
    nameLabel.setTextFill(Color.web("#333333"));
    Tooltip tooltip = new Tooltip();
    tooltip.textProperty().bind(nameLabel.textProperty());
    nameLabel.setTooltip(tooltip);

    Rotate rotate = new Rotate();
    rotate.angleProperty().bind(getCell().getAngleProperty());
    rotate.setPivotX(circle.getCenterX());
    rotate.setPivotY(circle.getCenterY());
    region.getTransforms().add(rotate);

    updateName();
    getCell().getBindedObjectProperty().addListener(change -> updateName());

    getCell()
        .getAngleProperty()
        .addListener((obs, oldVal, newVal) -> updateLabelRotate(newVal.doubleValue()));

    nameLabel
        .boundsInLocalProperty()
        .addListener(
            (obs, oldVal, newVal) -> updateLabelRotate(getCell().getAngleProperty().get()));

    getCell()
        .highLightProperty()
        .addListener(
            (obs, oldVal, newVal) -> {
              if (newVal) {
                circle.setStroke(SystemEditDefinition.LABEL_HIGHLIGHT_COLOR);
              } else {
                circle.setStroke(Color.web("#969899"));
              }
            });
  }

  private void updateLabelRotate(double angle) {
    nameLabel.getTransforms().clear();
    if (angle > 90 && angle < 270) {
      nameLabel
          .getTransforms()
          .add(
              new Rotate(
                  180, nameLabel.getBoundsInLocal().getWidth() / 2, nameLabel.getMinHeight() / 2));
      nameLabel.setTextAlignment(TextAlignment.LEFT);
      nameLabel.setAlignment(Pos.CENTER_LEFT);
    } else {
      nameLabel.setTextAlignment(TextAlignment.RIGHT);
      nameLabel.setAlignment(Pos.CENTER_RIGHT);
    }
  }

  @Override
  protected void onConnectorChange() {}

  @Override
  protected void layoutConnectors() {}

  @Override
  public Region getRegion() {
    return region;
  }

  @Override
  public Collection<ConnectorSkin> getConnectorSkins() {
    return connectorSkins.values();
  }

  @Override
  public Color getSelectionBorderColor() {
    return SystemeditConstants.SELECTED_COLOR;
  }

  @Override
  public boolean isSelectionEffectEnabled() {
    return false;
  }

  @Override
  public boolean isResizeble() {
    return false;
  }

  @Override
  public boolean isMovable() {
    return false;
  }

  ///
  protected void updateName() {
    CellBindedObject object = getCell().getBindedObject();
    if (object instanceof GridMatrixItem) {
      nameLabel.textProperty().bind(((GridMatrixItem) object).getNameProperty());
    }
  }
}
