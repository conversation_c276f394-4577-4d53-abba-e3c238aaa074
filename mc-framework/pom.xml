<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>
  <groupId>com.mc</groupId>
  <version>0.0.1-SNAPSHOT</version>
  <name>mc-framework</name>
  <properties>
    <checkstyle.violation.ignore>LineLength</checkstyle.violation.ignore>
    <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    <maven.jxr.version>3.1.1</maven.jxr.version>
    <maven.checkstyle.version>3.1.2</maven.checkstyle.version>
    <maven.pmd.version>3.9.0</maven.pmd.version>
    <maven.findbugs.version>3.0.5</maven.findbugs.version>
    <mc.common.version>0.0.1-SNAPSHOT</mc.common.version>
    <controlsfx.version>8.40.18</controlsfx.version>
    <guice.version>5.1.0</guice.version>
    <lombok.version>1.18.26</lombok.version>
    <slf4j.version>1.7.36</slf4j.version>
    <maven.compiler.version>3.9.0</maven.compiler.version>
    <javacv.version>1.5.9</javacv.version>
    <ffmpeg.version>4.1-1.4.4</ffmpeg.version>
    <log4j.version>2.20.0</log4j.version>
    <yaml.version>2.15.2</yaml.version>
  </properties>
  <reporting>
    <plugins>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-jxr-plugin</artifactId>
        <version>${maven.jxr.version}</version>
      </plugin>
    </plugins>
  </reporting>
  <build>
    <plugins>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-compiler-plugin</artifactId>
        <version>${maven.compiler.version}</version>
        <configuration>
          <source>1.8</source>
          <target>1.8</target>
          <encoding>utf-8</encoding>
        </configuration>
      </plugin>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-checkstyle-plugin</artifactId>
        <version>${maven.checkstyle.version}</version>
        <dependencies>
          <dependency>
            <groupId>com.puppycrawl.tools</groupId>
            <artifactId>checkstyle</artifactId>
            <version>9.0.1</version>
          </dependency>
        </dependencies>
        <configuration>
          <configLocation>../google_checks9.0.1.xml</configLocation>
          <encoding>UTF-8</encoding>
          <failsOnError>true</failsOnError>
          <failOnViolation>true</failOnViolation>
          <violationSeverity>info</violationSeverity>
          <suppressionsLocation>${basedir}/checkstyle-suppressions.xml</suppressionsLocation>
        </configuration>
        <executions>
          <execution>
            <id>checkstyle</id>
            <phase>validate</phase>
            <goals>
              <goal>check</goal>
            </goals>
            <configuration>
              <failOnViolation>true</failOnViolation>
            </configuration>
          </execution>
        </executions>
      </plugin>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-pmd-plugin</artifactId>
        <version>${maven.pmd.version}</version>
        <configuration>
          <excludes>
            <exclude>org/bytedeco/javacv/FfmpegFrameGrabberEx.java</exclude>
          </excludes>
          <analysisCache>true</analysisCache>
          <linkXRef>true</linkXRef>
        </configuration>
        <executions>
          <execution>
            <id>pmd</id>
            <phase>compile</phase>
            <goals>
              <goal>check</goal>
            </goals>
          </execution>
        </executions>
      </plugin>
      <plugin>
        <groupId>org.codehaus.mojo</groupId>
        <artifactId>findbugs-maven-plugin</artifactId>
        <version>${maven.findbugs.version}</version>
        <configuration>
          <effort>Max</effort>
          <threshold>Low</threshold>
          <xmlOutput>true</xmlOutput>
          <excludeFilterFile>findbugs-exclude.xml</excludeFilterFile>
        </configuration>
        <executions>
          <execution>
            <phase>compile</phase>
            <goals>
              <goal>check</goal>
            </goals>
          </execution>
        </executions>
      </plugin>
    </plugins>
  </build>
  <artifactId>mc-framework</artifactId>
  <dependencies>
    <dependency>
      <groupId>com.google.code.findbugs</groupId>
      <artifactId>annotations</artifactId>
      <version>3.0.1u2</version>
    </dependency>
    <dependency>
      <groupId>com.mc</groupId>
      <artifactId>mc-common</artifactId>
      <version>${mc.common.version}</version>
    </dependency>
    <dependency>
      <groupId>org.controlsfx</groupId>
      <artifactId>controlsfx</artifactId>
      <version>${controlsfx.version}</version>
    </dependency>
    <dependency>
      <groupId>com.google.inject</groupId>
      <artifactId>guice</artifactId>
      <version>${guice.version}</version>
    </dependency>
    <dependency>
      <groupId>org.projectlombok</groupId>
      <artifactId>lombok</artifactId>
      <version>${lombok.version}</version>
    </dependency>
    <dependency>
      <groupId>org.apache.logging.log4j</groupId>
      <artifactId>log4j-core</artifactId>
      <version>${log4j.version}</version>
    </dependency>
    <dependency>
      <groupId>org.apache.logging.log4j</groupId>
      <artifactId>log4j-slf4j-impl</artifactId>
      <version>${log4j.version}</version>
    </dependency>
    <dependency>
      <groupId>org.apache.logging.log4j</groupId>
      <artifactId>log4j-api</artifactId>
      <version>${log4j.version}</version>
    </dependency>
    <dependency>
      <groupId>org.slf4j</groupId>
      <artifactId>slf4j-api</artifactId>
      <version>${slf4j.version}</version>
    </dependency>
    <dependency>
      <groupId>org.slf4j</groupId>
      <artifactId>jul-to-slf4j</artifactId>
      <version>${slf4j.version}</version>
    </dependency>
    <dependency>
      <groupId>insidefx.undecorator</groupId>
      <artifactId>undecoratorex</artifactId>
      <version>0.1.0</version>
    </dependency>
    <dependency>
      <groupId>com.mc</groupId>
      <artifactId>mc-graph</artifactId>
      <version>0.0.1-SNAPSHOT</version>
    </dependency>
    <dependency>
      <groupId>junit</groupId>
      <artifactId>junit</artifactId>
      <version>4.13.2</version>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>org.hildan.fxgson</groupId>
      <artifactId>fx-gson</artifactId>
      <version>5.0.0</version>
    </dependency>
    <dependency>
      <groupId>com.dooapp.fxform2</groupId>
      <artifactId>core</artifactId>
      <version>8.2.11</version> <!-- Note: For JavaFX 2.2, use 2.2.6 -->
      <!--			<exclusions>-->
      <!--				<exclusion>-->
      <!--					<groupId>javax.validation</groupId>-->
      <!--					<artifactId>validation-api</artifactId>-->
      <!--				</exclusion>-->
      <!--				<exclusion>-->
      <!--					<groupId>org.hibernate</groupId>-->
      <!--					<artifactId>hibernate-validator</artifactId>-->
      <!--				</exclusion>-->
      <!--			</exclusions>-->
    </dependency>
    <dependency>
      <groupId>javax.validation</groupId>
      <artifactId>validation-api</artifactId>
      <version>2.0.1.Final</version>
    </dependency>
    <dependency>
      <groupId>org.hibernate</groupId>
      <artifactId>hibernate-validator</artifactId>
      <version>5.1.3.Final</version>
    </dependency>
    <dependency>
      <groupId>javax.el</groupId>
      <artifactId>el-api</artifactId>
      <version>2.2.1-b04</version>
    </dependency>
    <dependency>
      <groupId>org.glassfish.web</groupId>
      <artifactId>el-impl</artifactId>
      <version>2.2.1-b05</version>
    </dependency>
    <dependency>
      <groupId>net.java.dev.jna</groupId>
      <artifactId>jna-platform</artifactId>
      <version>5.12.1</version>
    </dependency>
    <dependency>
      <groupId>com.fasterxml.jackson.dataformat</groupId>
      <artifactId>jackson-dataformat-yaml</artifactId>
      <version>${yaml.version}</version>
    </dependency>
  </dependencies>
</project>