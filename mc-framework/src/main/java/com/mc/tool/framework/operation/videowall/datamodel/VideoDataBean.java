package com.mc.tool.framework.operation.videowall.datamodel;

import com.mc.tool.framework.operation.videowall.datamodel.interfaces.VideoObject;
import javafx.beans.property.IntegerProperty;
import javafx.beans.property.StringProperty;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;

/**
 * .
 */
public class VideoDataBean {
  private StringProperty name;

  private IntegerProperty xpos;

  private IntegerProperty ypos;

  private IntegerProperty width;

  private IntegerProperty height;

  /**
   * Constructor.
   *
   * @param data data
   */
  public VideoDataBean(VideoObject data) {
    name = data.getName();
    xpos = data.getXpos();
    ypos = data.getYpos();
    width = data.getWidth();
    height = data.getHeight();
  }

  @Size(min = 1)
  public String getName() {
    return name.get();
  }

  @NotNull
  public int getXpos() {
    return xpos.get();
  }

  @NotNull
  public int getYpos() {
    return ypos.get();
  }

  @Min(100)
  @NotNull
  public int getWidth() {
    return width.get();
  }

  @Min(100)
  @NotNull
  public int getHeight() {
    return height.get();
  }
}
